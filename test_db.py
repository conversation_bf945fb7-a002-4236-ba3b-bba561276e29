#!/usr/bin/env python3

import sqlite3
import sys
import os

# <PERSON><PERSON><PERSON><PERSON> do backend adres<PERSON><PERSON><PERSON>
backend_dir = "/home/<USER>/vyvoj/nszm/AI-korektor-titulku/backend"
os.chdir(backend_dir)

try:
    # Připoj se k databázi
    conn = sqlite3.connect('app.db')
    cursor = conn.cursor()
    
    print("=== STRUKTURA TABULKY PROJECTS ===")
    cursor.execute('PRAGMA table_info(projects)')
    columns = cursor.fetchall()
    for col in columns:
        print(f"  {col[1]} ({col[2]})")
    
    print("\n=== PROJEKTY V DATABÁZI ===")
    cursor.execute('SELECT project_id, youtube_url, video_title, status FROM projects')
    projects = cursor.fetchall()
    for project in projects:
        print(f"  {project[0]}: {project[2]} ({project[3]})")
    
    print(f"\nCelkem projektů: {len(projects)}")
    
    # Zkontroluj segmenty
    cursor.execute('SELECT COUNT(*) FROM subtitle_segments')
    segment_count = cursor.fetchone()[0]
    print(f"Celkem segmentů: {segment_count}")
    
    # Zkontroluj zdroje
    try:
        cursor.execute('SELECT COUNT(*) FROM subtitle_sources')
        source_count = cursor.fetchone()[0]
        print(f"Celkem zdrojů: {source_count}")
    except sqlite3.OperationalError as e:
        print(f"Chyba při načítání zdrojů: {e}")
    
    conn.close()
    print("\n✅ Test databáze dokončen")
    
except Exception as e:
    print(f"❌ Chyba: {e}")
    sys.exit(1)
