# Produktový Požadavkový Dokument (PRD): AI Korektor Titul<PERSON>ů "Helios"

| Verze | Datum       | Autor                          | Stav        |
| :---- | :---------- | :----------------------------- | :---------- |
| 2.1   | 14. 7. 2025 | Seniorní SW vývojář/Datový analytik | **Revidováno** |

## 1. <PERSON><PERSON><PERSON> (Executive Summary)

Tento dokument definuje technickou specifikaci pro desktopovou aplikaci **"Helios v1.0"** (d<PERSON><PERSON> jen "Aplikace"). Helios je nástroj pro asistovanou AI korekturu automaticky generovaných titulků z YouTube. Aplikace se integruje s externími AI službami (OpenAI) pro transkripci a sémantickou korek<PERSON>, č<PERSON><PERSON>ž eliminuje závislost na lokálním hardwaru. Cílem MVP (v1.0) je poskytnout uživateli vysoce produktivní prostředí pro zpracování jednoho videa, od zadání URL po export bezchybného `.srt` souboru.

### 1.1 Technologický Stack
- **Frontend:** Electron 25+ s React 18+ a TypeScript
- **Backend:** Python 3.12+ s FastAPI 0.100+
- **Databáze:** SQLite pro lokální úložiště (jednoduché nasazení)
- **Externí API:** OpenAI Whisper API, OpenAI GPT-4-Turbo API

### 1.2 Podporované formáty a omezení
- **Vstupní formáty:** YouTube URL (všechny podporované formáty yt-dlp)
- **Výstupní formáty:** .srt
- **Maximální délka videa:** 3 hodiny
- **Jazyky:** Čeština (primární), rozšíření na další jazyky v budoucích verzích

## 2. Architektura systému

### 2.1 Přehled komponent
```
┌─────────────────┐    HTTP/REST    ┌─────────────────┐
│  Electron App   │ ←──────────────→ │  FastAPI Server │
│  (Frontend)     │                 │  (Backend)      │
└─────────────────┘                 └─────────────────┘
                                             │
                                             ▼
                                    ┌─────────────────┐
                                    │  SQLite DB      │
                                    │  (Local)        │
                                    └─────────────────┘
                                             │
                                             ▼
                                    ┌─────────────────┐
                                    │  OpenAI APIs    │
                                    │  (External)     │
                                    └─────────────────┘
```

### 2.2 Komunikace a porty
- **Backend port:** 8001 (konfigurovatelný v .env)
- **Frontend port:** 3000 (konfigurovatelný v .env)
- **Frontend komunikuje s:** `http://localhost:8001`
- **Timeout nastavení:** 
  - API calls: 30 sekund
  - Video processing: 10 minut
  - File operations: 5 minut

### 2.3 Workflow aplikace (AKTUALIZOVÁNO v2.1)

#### Nový manuální workflow:
1. **Načtení videa** - Uživatel zadá YouTube URL
   - Systém pouze načte metadata (název, délka)
   - Status: `loaded` - video připraveno k zpracování

2. **Manuální akce** - Uživatel řídí každý krok pomocí tlačítek v levém panelu:
   - 🗑️ **Smazat projekt** - Odstraní projekt
   - 📄 **Načíst titulky** - Extrahuje existující titulky z YouTube
     - Zobrazí dostupné jazyky (manuální + automatické)
     - Uživatel vybere jazyk
     - Status: `subtitles_extracted`
   - 📥 **Stáhnout audio** - Stáhne audio track z videa
     - Status: `audio_downloaded`
   - 🎤 **Whisper transkripce** - Převede audio na text pomocí AI
     - Status: `whisper_transcribed`
   - 🪄 **AI korekce** - Opraví text pomocí GPT-4o-mini
     - Status: `ai_corrected`

3. **Review a úpravy** - Uživatel může:
   - Prohlížet a upravovat jednotlivé segmenty
   - Přijímat/zamítat AI návrhy
   - Přidávat vlastní korekce
   - Status: `needs_review`

4. **Export** - Finální export do SRT formátu
   - Preview s validací a statistikami
   - Stažení SRT souboru

#### Výhody nového workflow:
- **Uživatelská kontrola** - Každý krok je řízený uživatelem
- **Flexibilita** - Možnost přeskočit kroky nebo je opakovat
- **Transparentnost** - Jasné zobrazení stavu a pokroku
- **Efektivita** - Žádné zbytečné automatické zpracování

### 2.4 Zabezpečení
- **OpenAI API Key:** Uložen v systémových proměnných prostředí
- **CORS:** Povoleno pouze pro localhost
- **Rate limiting:** 10 requests/minute pro projekt operace

## 3. Databázové schéma (SQLite)

### 3.1 Tabulka projects
```sql
CREATE TABLE projects (
    project_id TEXT PRIMARY KEY,
    youtube_url TEXT NOT NULL,
    video_title TEXT,
    video_duration INTEGER, -- v sekundách
    status TEXT NOT NULL CHECK (status IN ('processing', 'needs_review', 'completed', 'error')),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    error_message TEXT,
    audio_file_path TEXT,
    original_subtitles_path TEXT,
    reference_transcript TEXT
);
```

### 3.2 Tabulka subtitle_segments
```sql
CREATE TABLE subtitle_segments (
    segment_id TEXT PRIMARY KEY,
    project_id TEXT NOT NULL,
    sequence_number INTEGER NOT NULL,
    start_time_ms INTEGER NOT NULL,
    end_time_ms INTEGER NOT NULL,
    original_text TEXT NOT NULL,
    corrected_text TEXT NOT NULL,
    status TEXT NOT NULL CHECK (status IN ('unchanged', 'auto_corrected', 'needs_review', 'user_modified')),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (project_id) REFERENCES projects(project_id) ON DELETE CASCADE
);
```

### 3.3 Tabulka correction_suggestions
```sql
CREATE TABLE correction_suggestions (
    suggestion_id TEXT PRIMARY KEY,
    segment_id TEXT NOT NULL,
    type TEXT NOT NULL CHECK (type IN ('dictionary_match', 'punctuation', 'capitalization', 'semantic_rewrite')),
    confidence REAL NOT NULL CHECK (confidence >= 0.0 AND confidence <= 1.0),
    description TEXT NOT NULL,
    original_fragment TEXT NOT NULL,
    suggested_fragment TEXT NOT NULL,
    applied BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (segment_id) REFERENCES subtitle_segments(segment_id) ON DELETE CASCADE
);
```

### 3.4 Tabulka user_dictionary
```sql
CREATE TABLE user_dictionary (
    term_id TEXT PRIMARY KEY,
    phrase TEXT NOT NULL UNIQUE,
    case_sensitive BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

## 4. Core Data Models (JSON API struktury)

### 4.1 Project
```json
{
  "projectId": "uuid-v4-string",
  "youtubeUrl": "https://www.youtube.com/watch?v=dQw4w9WgXcQ",
  "videoTitle": "Never Gonna Give You Up",
  "videoDuration": 212,
  "status": "processing" | "needs_review" | "completed" | "error",
  "createdAt": "2025-07-14T10:30:00Z",
  "updatedAt": "2025-07-14T10:35:00Z",
  "errorMessage": null,
  "segments": [],
  "statistics": {
    "totalSegments": 45,
    "needsReviewCount": 8,
    "autoCorrectionsCount": 12,
    "userModifiedCount": 0
  }
}
```

### 4.2 SubtitleSegment
```json
{
  "segmentId": "uuid-v4-string",
  "sequenceNumber": 1,
  "startTimeMs": 15250,
  "endTimeMs": 18100,
  "originalText": "zdravá města jsou organizace",
  "correctedText": "Zdravá města jsou organizace",
  "status": "unchanged" | "auto_corrected" | "needs_review" | "user_modified",
  "suggestions": []
}
```

### 4.3 CorrectionSuggestion
```json
{
  "suggestionId": "uuid-v4-string",
  "type": "dictionary_match" | "punctuation" | "capitalization" | "semantic_rewrite",
  "confidence": 0.95,
  "description": "Pravidlo ze slovníku: 'Zdravá města'",
  "originalFragment": "zdravá města",
  "suggestedFragment": "Zdravá města",
  "applied": false
}
```

### 4.4 UserDictionaryTerm
```json
{
  "termId": "uuid-v4-string",
  "phrase": "Zdravá města",
  "caseSensitive": true,
  "createdAt": "2025-07-14T10:30:00Z"
}
```

## 5. Backend: Implementační specifikace

### 5.1 Adresářová struktura
```
backend/
├── app/
│   ├── __init__.py
│   ├── main.py              # FastAPI app
│   ├── config.py            # Konfigurace
│   ├── database.py          # SQLite connection
│   ├── models/
│   │   ├── __init__.py
│   │   ├── project.py       # Pydantic models
│   │   └── dictionary.py
│   ├── services/
│   │   ├── __init__.py
│   │   ├── youtube_service.py
│   │   ├── openai_service.py
│   │   └── processing_service.py
│   ├── api/
│   │   ├── __init__.py
│   │   ├── projects.py      # Project endpoints
│   │   └── dictionary.py    # Dictionary endpoints
│   └── utils/
│       ├── __init__.py
│       ├── srt_generator.py
│       └── validators.py
├── requirements.txt
├── tests/
└── temp/                    # Dočasné soubory
```

### 5.2 Konfigurace (config.py)
```python
from pydantic_settings import BaseSettings
from typing import Optional

class Settings(BaseSettings):
    # Databáze
    DATABASE_URL: str = "sqlite:///./ai_korektor.db"

    # OpenAI
    OPENAI_API_KEY: str

    # Server
    HOST: str = "0.0.0.0"
    PORT: int = 8000
    DEBUG: bool = False

    # CORS
    ALLOWED_ORIGINS: list[str] = ["http://localhost:3000", "http://127.0.0.1:3000"]

    # Logging
    LOG_LEVEL: str = "INFO"

    class Config:
        env_file = ".env"
        case_sensitive = True

settings = Settings()
```

### 5.4 Prostředí (.env)
```bash
# OpenAI API klíč (povinný)
OPENAI_API_KEY=sk-your-openai-api-key-here

# Databáze
DATABASE_URL=sqlite:///./ai_korektor.db

# Server konfigurace
HOST=0.0.0.0
PORT=8000
DEBUG=False

# CORS
ALLOWED_ORIGINS=["http://localhost:3000","http://127.0.0.1:3000"]

# Zpracování
TEMP_DIR=./temp
MAX_VIDEO_DURATION=10800
WHISPER_MODEL=whisper-1
GPT_MODEL=gpt-4o-mini
CONFIDENCE_THRESHOLD=0.9

# Logging
LOG_LEVEL=INFO
```

### 5.5 Závislosti (requirements.txt)
```
fastapi==0.115.6
uvicorn==0.32.1
pydantic==2.10.4
pydantic-settings==2.7.0
sqlalchemy==2.0.36
yt-dlp==2024.12.13
openai==1.68.0
python-multipart==0.0.20
python-dotenv==1.0.1
pytest==8.3.4
httpx==0.28.1
```

### 5.3 Konfigurace (config.py)
```python
from pydantic_settings import BaseSettings
from typing import List

class Settings(BaseSettings):
    # Databáze
    DATABASE_URL: str = "sqlite:///./ai_korektor.db"

    # OpenAI
    OPENAI_API_KEY: str

    # Server
    HOST: str = "0.0.0.0"
    PORT: int = 8000
    DEBUG: bool = False

    # CORS
    ALLOWED_ORIGINS: List[str] = ["http://localhost:3000", "http://127.0.0.1:3000"]

    # Zpracování
    TEMP_DIR: str = "./temp"
    MAX_VIDEO_DURATION: int = 10800  # 3 hodiny v sekundách
    WHISPER_MODEL: str = "whisper-1"
    GPT_MODEL: str = "gpt-4o-mini"
    CONFIDENCE_THRESHOLD: float = 0.9

    # Logging
    LOG_LEVEL: str = "INFO"

    model_config = {"env_file": ".env", "case_sensitive": True}

settings = Settings()
```

### 5.6 Databáze (database.py)
```python
from sqlalchemy import create_engine
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
from .config import settings

engine = create_engine(
    settings.DATABASE_URL,
    connect_args={"check_same_thread": False} if "sqlite" in settings.DATABASE_URL else {}
)

SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
Base = declarative_base()

def get_db():
    """Dependency pro získání databázové session"""
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

def create_tables():
    """Vytvoří všechny tabulky"""
    Base.metadata.create_all(bind=engine)
```

### 5.7 Databázové modely (models.py)
```python
from sqlalchemy import Column, String, Integer, DateTime, Text, Boolean, Float, ForeignKey
from sqlalchemy.orm import relationship
from .database import Base
from datetime import datetime

class Project(Base):
    __tablename__ = "projects"

    project_id = Column(String, primary_key=True)
    youtube_url = Column(String, nullable=False)
    video_title = Column(String)
    video_duration = Column(Integer)
    status = Column(String, default="PENDING")
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    error_message = Column(Text)

    # Vztahy
    segments = relationship("SubtitleSegment", back_populates="project", cascade="all, delete-orphan")

class SubtitleSegment(Base):
    __tablename__ = "subtitle_segments"

    segment_id = Column(String, primary_key=True)
    project_id = Column(String, ForeignKey("projects.project_id"), nullable=False)
    sequence_number = Column(Integer, nullable=False)
    start_time_ms = Column(Integer, nullable=False)
    end_time_ms = Column(Integer, nullable=False)
    original_text = Column(Text, nullable=False)
    corrected_text = Column(Text, nullable=False)
    status = Column(String, default="NEEDS_REVIEW")

    # Vztahy
    project = relationship("Project", back_populates="segments")
    suggestions = relationship("CorrectionSuggestion", back_populates="segment", cascade="all, delete-orphan")

class CorrectionSuggestion(Base):
    __tablename__ = "correction_suggestions"

    suggestion_id = Column(String, primary_key=True)
    segment_id = Column(String, ForeignKey("subtitle_segments.segment_id"), nullable=False)
    type = Column(String, nullable=False)
    confidence = Column(Float, nullable=False)
    description = Column(Text)
    original_fragment = Column(Text, nullable=False)
    suggested_fragment = Column(Text, nullable=False)
    applied = Column(Boolean, default=False)

    # Vztahy
    segment = relationship("SubtitleSegment", back_populates="suggestions")

class UserDictionaryTerm(Base):
    __tablename__ = "user_dictionary_terms"

    term_id = Column(String, primary_key=True)
    phrase = Column(String, nullable=False)
    case_sensitive = Column(Boolean, default=True)
    created_at = Column(DateTime, default=datetime.utcnow)
```

### 5.8 Zpracovatelský Pipeline (processing_service.py)

#### 5.8.1 Hlavní funkce process_video
```python
async def process_video(project_id: str):
    """
    Hlavní asynchronní funkce pro zpracování videa
    """
    try:
        # 1. Aktualizuj status na processing
        await update_project_status(project_id, "processing")
        
        # 2. Validace YouTube URL
        youtube_url = await get_project_youtube_url(project_id)
        if not validate_youtube_url(youtube_url):
            raise ValueError("Invalid YouTube URL")
        
        # 3. Extrakce metadat
        video_info = await extract_video_info(youtube_url)
        await update_project_metadata(project_id, video_info)
        
        # 4. Stažení audio a titulků
        audio_path = await download_audio(youtube_url, project_id)
        original_subtitles = await extract_original_subtitles(youtube_url)
        
        # 5. Whisper transkripce
        reference_transcript = await transcribe_with_whisper(audio_path)
        
        # 6. Parsování originálních titulků
        segments = await parse_original_subtitles(original_subtitles)
        
        # 7. AI korekce
        corrected_segments = await apply_ai_corrections(segments, reference_transcript, project_id)
        
        # 8. Uložení do databáze
        await save_segments_to_db(project_id, corrected_segments)
        
        # 9. Finální status
        await update_project_status(project_id, "needs_review")
        
    except Exception as e:
        await update_project_status(project_id, "error", str(e))
        raise
```

#### 5.8.2 YouTube processing
```python
async def download_audio(youtube_url: str, project_id: str) -> str:
    """
    Stahuje audio ze YouTube pomocí yt-dlp
    """
    output_path = f"{settings.temp_dir}/{project_id}_audio.mp3"
    
    ydl_opts = {
        'format': 'bestaudio/best',
        'postprocessors': [{
            'key': 'FFmpegExtractAudio',
            'preferredcodec': 'mp3',
            'preferredquality': '192',
        }],
        'outtmpl': output_path,
    }
    
    with yt_dlp.YoutubeDL(ydl_opts) as ydl:
        ydl.download([youtube_url])
    
    return output_path

async def extract_original_subtitles(youtube_url: str) -> str:
    """
    Extrahuje originální titulky (pokud jsou dostupné)
    """
    ydl_opts = {
        'writesubtitles': True,
        'writeautomaticsub': True,
        'subtitleslangs': ['cs', 'cs-CZ'],
        'skip_download': True,
    }
    
    with yt_dlp.YoutubeDL(ydl_opts) as ydl:
        info = ydl.extract_info(youtube_url, download=False)
        # Detailní logika pro extrakci titulků
        # Vrací text nebo None pokud nejsou dostupné
```

#### 5.8.3 OpenAI integrace
```python
async def transcribe_with_whisper(audio_path: str) -> str:
    """
    Transkribuje audio pomocí OpenAI Whisper
    """
    client = OpenAI(api_key=settings.openai_api_key)
    
    with open(audio_path, "rb") as audio_file:
        response = client.audio.transcriptions.create(
            model=settings.whisper_model,
            file=audio_file,
            language="cs"
        )
    
    return response.text

async def apply_ai_corrections(segments: List[SubtitleSegment], reference_text: str, project_id: str) -> List[SubtitleSegment]:
    """
    Aplikuje AI korekce na segmenty
    """
    # Načti uživatelský slovník
    dictionary_terms = await get_user_dictionary()
    
    # Sestav prompt pro GPT
    prompt = await build_correction_prompt(segments, reference_text, dictionary_terms)
    
    # Volej GPT API
    client = OpenAI(api_key=settings.openai_api_key)
    response = client.chat.completions.create(
        model=settings.gpt_model,
        messages=[
            {"role": "system", "content": "Jsi expert na korekturu českých titulků."},
            {"role": "user", "content": prompt}
        ],
        temperature=0.1
    )
    
    # Parsuj JSON odpověď
    corrections = json.loads(response.choices[0].message.content)
    
    # Aplikuj korekce na segmenty
    return await apply_corrections_to_segments(segments, corrections)
```

### 5.5 Pydantic Request/Response Models

```python
from pydantic import BaseModel, Field
from typing import List, Optional
from datetime import datetime
from enum import Enum

class ProjectStatus(str, Enum):
    PROCESSING = "processing"
    NEEDS_REVIEW = "needs_review"
    COMPLETED = "completed"
    ERROR = "error"

class SegmentStatus(str, Enum):
    UNCHANGED = "unchanged"
    AUTO_CORRECTED = "auto_corrected"
    NEEDS_REVIEW = "needs_review"
    USER_MODIFIED = "user_modified"

class SuggestionType(str, Enum):
    DICTIONARY_MATCH = "dictionary_match"
    PUNCTUATION = "punctuation"
    CAPITALIZATION = "capitalization"
    SEMANTIC_REWRITE = "semantic_rewrite"

# Request Models
class CreateProjectRequest(BaseModel):
    youtube_url: str = Field(..., description="YouTube URL videa")

class UpdateSegmentRequest(BaseModel):
    corrected_text: str = Field(..., description="Opravený text segmentu")

class CreateDictionaryTermRequest(BaseModel):
    phrase: str = Field(..., description="Termín do slovníku")
    case_sensitive: bool = Field(True, description="Rozlišovat velikost písmen")

# Response Models
class CorrectionSuggestion(BaseModel):
    suggestion_id: str = Field(..., alias="suggestionId")
    type: SuggestionType
    confidence: float = Field(..., ge=0.0, le=1.0)
    description: str
    original_fragment: str = Field(..., alias="originalFragment")
    suggested_fragment: str = Field(..., alias="suggestedFragment")
    applied: bool = False

class SubtitleSegment(BaseModel):
    segment_id: str = Field(..., alias="segmentId")
    sequence_number: int = Field(..., alias="sequenceNumber")
    start_time_ms: int = Field(..., alias="startTimeMs")
    end_time_ms: int = Field(..., alias="endTimeMs")
    original_text: str = Field(..., alias="originalText")
    corrected_text: str = Field(..., alias="correctedText")
    status: SegmentStatus
    suggestions: List[CorrectionSuggestion] = []

class ProjectStatistics(BaseModel):
    total_segments: int = Field(..., alias="totalSegments")
    needs_review_count: int = Field(..., alias="needsReviewCount")
    auto_corrections_count: int = Field(..., alias="autoCorrectionsCount")
    user_modified_count: int = Field(..., alias="userModifiedCount")

class ProjectSummary(BaseModel):
    project_id: str = Field(..., alias="projectId")
    youtube_url: str = Field(..., alias="youtubeUrl")
    video_title: Optional[str] = Field(None, alias="videoTitle")
    video_duration: Optional[int] = Field(None, alias="videoDuration")
    status: ProjectStatus
    created_at: datetime = Field(..., alias="createdAt")
    updated_at: datetime = Field(..., alias="updatedAt")
    error_message: Optional[str] = Field(None, alias="errorMessage")

class Project(ProjectSummary):
    segments: List[SubtitleSegment] = []
    statistics: ProjectStatistics

class UserDictionaryTerm(BaseModel):
    term_id: str = Field(..., alias="termId")
    phrase: str
    case_sensitive: bool = Field(..., alias="caseSensitive")
    created_at: datetime = Field(..., alias="createdAt")
```

### 5.9 Služby (Services)

#### 5.9.1 ProjectService
```python
from sqlalchemy.orm import Session
from .models import Project as ProjectModel, SubtitleSegment as SegmentModel
from .schemas import Project, ProjectSummary
from .youtube_service import YouTubeService
from .ai_service import AIService
import uuid
from datetime import datetime

class ProjectService:
    def __init__(self, db: Session):
        self.db = db
        self.youtube_service = YouTubeService()
        self.ai_service = AIService()

    def create_project(self, youtube_url: str) -> Project:
        """Vytvoří nový projekt v databázi"""
        project_id = str(uuid.uuid4())

        project = ProjectModel(
            project_id=project_id,
            youtube_url=youtube_url,
            status="PENDING",
            created_at=datetime.utcnow(),
            updated_at=datetime.utcnow()
        )

        self.db.add(project)
        self.db.commit()
        self.db.refresh(project)

        return Project.model_validate(project)

    def get_all_projects(self) -> List[ProjectSummary]:
        """Vrátí seznam všech projektů"""
        projects = self.db.query(ProjectModel).all()
        return [ProjectSummary.model_validate(p) for p in projects]

    def get_project_by_id(self, project_id: str) -> Optional[Project]:
        """Vrátí projekt podle ID včetně segmentů"""
        project = self.db.query(ProjectModel).filter(
            ProjectModel.project_id == project_id
        ).first()

        if not project:
            return None

        return Project.model_validate(project)

    def process_video(self, project_id: str):
        """Zpracuje video na pozadí"""
        try:
            project = self.db.query(ProjectModel).filter(
                ProjectModel.project_id == project_id
            ).first()

            if not project:
                return

            # Aktualizuj status na PROCESSING
            project.status = "PROCESSING"
            self.db.commit()

            # Stáhni video info
            video_info = self.youtube_service.get_video_info(project.youtube_url)
            project.video_title = video_info.get("title")
            project.video_duration = video_info.get("duration")

            # Extrahuj titulky
            subtitles = self.youtube_service.extract_subtitles(project.youtube_url)

            # Zpracuj AI korekce
            corrected_segments = self.ai_service.process_subtitles(subtitles)

            # Ulož segmenty do databáze
            for i, segment in enumerate(corrected_segments):
                segment_model = SegmentModel(
                    segment_id=str(uuid.uuid4()),
                    project_id=project_id,
                    sequence_number=i + 1,
                    start_time_ms=segment["start_time_ms"],
                    end_time_ms=segment["end_time_ms"],
                    original_text=segment["original_text"],
                    corrected_text=segment["corrected_text"],
                    status="NEEDS_REVIEW" if segment["has_corrections"] else "APPROVED"
                )
                self.db.add(segment_model)

            project.status = "COMPLETED"
            project.updated_at = datetime.utcnow()
            self.db.commit()

        except Exception as e:
            project.status = "ERROR"
            project.error_message = str(e)
            project.updated_at = datetime.utcnow()
            self.db.commit()
```

#### 5.9.2 YouTubeService
```python
import yt_dlp
from typing import Dict, List, Any

class YouTubeService:
    @staticmethod
    def is_valid_youtube_url(url: str) -> bool:
        """Validuje YouTube URL"""
        youtube_patterns = [
            r'youtube\.com/watch\?v=',
            r'youtu\.be/',
            r'youtube\.com/embed/',
            r'youtube\.com/v/'
        ]
        import re
        return any(re.search(pattern, url) for pattern in youtube_patterns)

    def get_video_info(self, url: str) -> Dict[str, Any]:
        """Získá informace o videu"""
        ydl_opts = {
            'quiet': True,
            'no_warnings': True,
        }

        with yt_dlp.YoutubeDL(ydl_opts) as ydl:
            info = ydl.extract_info(url, download=False)
            return {
                "title": info.get("title"),
                "duration": info.get("duration"),
                "description": info.get("description"),
                "uploader": info.get("uploader")
            }

    def extract_subtitles(self, url: str) -> List[Dict[str, Any]]:
        """Extrahuje titulky z videa"""
        ydl_opts = {
            'writesubtitles': True,
            'writeautomaticsub': True,
            'subtitleslangs': ['cs', 'en'],
            'skip_download': True,
            'quiet': True,
        }

        with yt_dlp.YoutubeDL(ydl_opts) as ydl:
            info = ydl.extract_info(url, download=False)

            # Zpracuj titulky do strukturovaného formátu
            subtitles = []
            if 'subtitles' in info or 'automatic_captions' in info:
                # Implementace parsování titulků
                pass

            return subtitles
```

#### 5.9.3 AIService
```python
from openai import OpenAI
from typing import List, Dict, Any
from .config import settings

class AIService:
    def __init__(self):
        self.client = OpenAI(api_key=settings.OPENAI_API_KEY)

    def process_subtitles(self, subtitles: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Zpracuje titulky pomocí AI pro korekce"""
        corrected_segments = []

        for segment in subtitles:
            original_text = segment["text"]

            # Pošli text na korekci
            corrected_text = self.correct_text(original_text)

            corrected_segments.append({
                "start_time_ms": segment["start_time_ms"],
                "end_time_ms": segment["end_time_ms"],
                "original_text": original_text,
                "corrected_text": corrected_text,
                "has_corrections": original_text != corrected_text
            })

        return corrected_segments

    def correct_text(self, text: str) -> str:
        """Opraví text pomocí OpenAI"""
        try:
            response = self.client.chat.completions.create(
                model="gpt-4o-mini",
                messages=[
                    {
                        "role": "system",
                        "content": """Jsi expert na korekci českých titulků. Oprav gramatické chyby,
                        překlepy a zlepši čitelnost textu. Zachovej původní význam a styl.
                        Vrať pouze opravený text bez dalších komentářů."""
                    },
                    {
                        "role": "user",
                        "content": f"Oprav tento text: {text}"
                    }
                ],
                temperature=0.1,
                max_tokens=500
            )

            return response.choices[0].message.content.strip()
        except Exception as e:
            # V případě chyby vrať původní text
            return text

    def generate_suggestions(self, text: str) -> List[Dict[str, Any]]:
        """Generuje návrhy na zlepšení textu"""
        try:
            response = self.client.chat.completions.create(
                model="gpt-4o-mini",
                messages=[
                    {
                        "role": "system",
                        "content": """Analyzuj text a navrhni konkrétní zlepšení.
                        Vrať JSON seznam návrhů s poli: type, confidence, description,
                        original_fragment, suggested_fragment."""
                    },
                    {
                        "role": "user",
                        "content": text
                    }
                ],
                temperature=0.2,
                max_tokens=1000
            )

            # Parsuj JSON odpověď
            import json
            suggestions = json.loads(response.choices[0].message.content)
            return suggestions
        except Exception:
            return []
```

#### 5.9.4 DictionaryService
```python
from sqlalchemy.orm import Session
from .models import UserDictionaryTerm as DictionaryModel
from .schemas import UserDictionaryTerm, CreateDictionaryTermRequest
import uuid
from datetime import datetime

class DictionaryService:
    def __init__(self, db: Session):
        self.db = db

    def get_all_dictionary_terms(self) -> List[UserDictionaryTerm]:
        """Vrátí všechny termíny ze slovníku"""
        terms = self.db.query(DictionaryModel).all()
        return [UserDictionaryTerm.model_validate(term) for term in terms]

    def create_dictionary_term(self, term_data: CreateDictionaryTermRequest) -> UserDictionaryTerm:
        """Přidá nový termín do slovníku"""
        term = DictionaryModel(
            term_id=str(uuid.uuid4()),
            phrase=term_data.phrase,
            case_sensitive=term_data.case_sensitive,
            created_at=datetime.utcnow()
        )

        self.db.add(term)
        self.db.commit()
        self.db.refresh(term)

        return UserDictionaryTerm.model_validate(term)

    def delete_dictionary_term_by_id(self, term_id: str) -> bool:
        """Smaže termín ze slovníku"""
        term = self.db.query(DictionaryModel).filter(
            DictionaryModel.term_id == term_id
        ).first()

        if not term:
            return False

        self.db.delete(term)
        self.db.commit()
        return True
```

### 5.10 Hlavní aplikace (main.py)
```python
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from .config import settings
from .database import create_tables
from .api.projects import router as projects_router
from .api.dictionary import dictionary_router
import logging

# Konfigurace loggingu
logging.basicConfig(level=settings.LOG_LEVEL)
logger = logging.getLogger(__name__)

# Vytvoření FastAPI aplikace
app = FastAPI(
    title="AI Korektor Titulků",
    description="API pro automatickou korekci českých titulků pomocí AI",
    version="1.0.0"
)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.ALLOWED_ORIGINS,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Registrace routerů
app.include_router(projects_router)
app.include_router(dictionary_router)

@app.on_event("startup")
async def startup_event():
    """Inicializace při startu aplikace"""
    logger.info("Spouštím AI Korektor Titulků...")
    create_tables()
    logger.info("Databáze inicializována")

@app.get("/")
async def root():
    """Health check endpoint"""
    return {"message": "AI Korektor Titulků API", "status": "running"}

@app.get("/health")
async def health_check():
    """Detailní health check"""
    return {
        "status": "healthy",
        "version": "1.0.0",
        "database": "connected"
    }

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "main:app",
        host=settings.HOST,
        port=settings.PORT,
        reload=settings.DEBUG,
        log_level=settings.LOG_LEVEL.lower()
    )
```

### 5.11 API Endpoints

#### 5.11.1 Projects API
```python
from fastapi import APIRouter, HTTPException, BackgroundTasks, Depends
from sqlalchemy.orm import Session
from typing import List
from .database import get_db
from .schemas import Project, ProjectSummary, CreateProjectRequest, UpdateSegmentRequest
from .services import ProjectService, YouTubeService

router = APIRouter(prefix="/api/v1/projects", tags=["projects"])

@router.post("/", response_model=Project)
async def create_project(
    project_data: CreateProjectRequest,
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_db)
):
    """
    Vytvoří nový projekt a spustí zpracování na pozadí
    """
    # Validace YouTube URL
    if not YouTubeService.is_valid_youtube_url(project_data.youtube_url):
        raise HTTPException(status_code=400, detail="Invalid YouTube URL")

    # Vytvoř projekt v databázi
    project_service = ProjectService(db)
    project = project_service.create_project(project_data.youtube_url)

    # Spusť zpracování na pozadí
    background_tasks.add_task(project_service.process_video, project.project_id)

    return project

@router.get("/", response_model=List[ProjectSummary])
async def get_projects(db: Session = Depends(get_db)):
    """
    Vrátí seznam všech projektů bez detailů segmentů
    """
    project_service = ProjectService(db)
    return project_service.get_all_projects()

@router.get("/{project_id}", response_model=Project)
async def get_project(project_id: str, db: Session = Depends(get_db)):
    """
    Vrátí detailní informace o projektu včetně segmentů
    """
    project_service = ProjectService(db)
    project = project_service.get_project_by_id(project_id)
    if not project:
        raise HTTPException(status_code=404, detail="Project not found")
    return project

@router.put("/{project_id}/segments/{segment_id}", response_model=SubtitleSegment)
async def update_segment(
    project_id: str,
    segment_id: str,
    update_data: UpdateSegmentRequest,
    db: Session = Depends(get_db)
):
    """
    Aktualizuje text segmentu (uživatelská editace)
    """
    project_service = ProjectService(db)
    segment = project_service.update_segment_text(segment_id, update_data.corrected_text)
    if not segment:
        raise HTTPException(status_code=404, detail="Segment not found")
    return segment

@router.post("/{project_id}/segments/{segment_id}/suggestions/{suggestion_id}/apply")
async def apply_suggestion(
    project_id: str,
    segment_id: str,
    suggestion_id: str,
    db: Session = Depends(get_db)
):
    """
    Aplikuje konkrétní návrh korekce
    """
    project_service = ProjectService(db)
    success = project_service.apply_correction_suggestion(suggestion_id)
    if not success:
        raise HTTPException(status_code=404, detail="Suggestion not found")
    return {"success": True}

@router.delete("/{project_id}")
async def delete_project(project_id: str, db: Session = Depends(get_db)):
    """
    Smaže projekt a všechna související data
    """
    project_service = ProjectService(db)
    success = project_service.delete_project(project_id)
    if not success:
        raise HTTPException(status_code=404, detail="Project not found")
    return {"message": "Project deleted successfully"}

@router.post("/{project_id}/export")
async def export_project(project_id: str, db: Session = Depends(get_db)):
    """
    Exportuje projekt do SRT formátu
    """
    from fastapi.responses import Response

    project_service = ProjectService(db)
    srt_content = project_service.generate_srt_file(project_id)
    if not srt_content:
        raise HTTPException(status_code=404, detail="Project not found")

    return Response(
        content=srt_content,
        media_type="text/plain",
        headers={"Content-Disposition": "attachment; filename=subtitles.srt"}
    )
```

#### 5.11.2 Dictionary API
```python
from .services import DictionaryService
from .schemas import UserDictionaryTerm, CreateDictionaryTermRequest

dictionary_router = APIRouter(prefix="/api/v1/dictionary", tags=["dictionary"])

@dictionary_router.get("/", response_model=List[UserDictionaryTerm])
async def get_dictionary(db: Session = Depends(get_db)):
    """
    Vrátí všechny termíny z uživatelského slovníku
    """
    dictionary_service = DictionaryService(db)
    return dictionary_service.get_all_dictionary_terms()

@dictionary_router.post("/", response_model=UserDictionaryTerm)
async def add_dictionary_term(
    term_data: CreateDictionaryTermRequest,
    db: Session = Depends(get_db)
):
    """
    Přidá nový termín do slovníku
    """
    dictionary_service = DictionaryService(db)
    return dictionary_service.create_dictionary_term(term_data)

@dictionary_router.delete("/{term_id}")
async def delete_dictionary_term(term_id: str, db: Session = Depends(get_db)):
    """
    Smaže termín ze slovníku
    """
    dictionary_service = DictionaryService(db)
    success = dictionary_service.delete_dictionary_term_by_id(term_id)
    if not success:
        raise HTTPException(status_code=404, detail="Term not found")
    return {"success": True}
```

### 5.12 Spuštění aplikace

#### 5.12.1 Instalace závislostí
```bash
# Backend
cd backend
pip install -r requirements.txt

# Frontend
cd frontend
npm install
```

#### 5.12.2 Konfigurace prostředí
```bash
# Zkopíruj a uprav .env soubor
cp .env.example .env
# Nastav OPENAI_API_KEY a další proměnné
```

#### 5.12.3 Spuštění pro vývoj
```bash
# Backend (v jednom terminálu)
cd backend
uvicorn main:app --reload --host 0.0.0.0 --port 8000

# Frontend (v druhém terminálu)
cd frontend
npm run dev
```

#### 5.12.4 Spuštění pro produkci
```bash
# Backend
cd backend
uvicorn main:app --host 0.0.0.0 --port 8000

# Frontend - build a spuštění
cd frontend
npm run build
npm run electron
```

## 6. Frontend: Implementační specifikace

### 6.1 Adresářová struktura
```
frontend/
├── src/
│   ├── main.ts              # Electron main process
│   ├── renderer.ts          # Electron renderer
│   ├── preload.ts           # Preload script
│   ├── components/
│   │   ├── ProjectListPanel.tsx
│   │   ├── EditorPanel.tsx
│   │   ├── ActionPanel.tsx
│   │   ├── VideoPlayer.tsx
│   │   ├── SubtitleSegmentRow.tsx
│   │   └── DictionaryManager.tsx
│   ├── services/
│   │   ├── api.ts           # API client
│   │   └── types.ts         # TypeScript typy
│   ├── hooks/
│   │   ├── useProject.ts
│   │   └── useKeyboard.ts
│   ├── utils/
│   │   └── time.ts          # Time formatting
│   └── styles/
│       └── global.css
├── package.json
├── webpack.config.js
├── tsconfig.json
└── electron-builder.json
```

### 6.2 Závislosti (package.json)
```json
{
  "dependencies": {
    "electron": "^33.2.1",
    "react": "^18.3.1",
    "react-dom": "^18.3.1",
    "react-player": "^2.16.0",
    "axios": "^1.7.9",
    "uuid": "^11.0.3"
  },
  "devDependencies": {
    "@types/react": "^18.3.17",
    "@types/react-dom": "^18.3.5",
    "typescript": "^5.7.2",
    "webpack": "^5.97.1",
    "electron-builder": "^25.1.8"
  }
}
```

### 6.3 API Client (services/api.ts)
```typescript
import axios from 'axios';

const API_BASE_URL = 'http://localhost:8000/api/v1';

const apiClient = axios.create({
  baseURL: API_BASE_URL,
  timeout: 30000,
});

export const projectsApi = {
  async createProject(youtubeUrl: string): Promise<Project> {
    const response = await apiClient.post('/projects', { youtubeUrl });
    return response.data;
  },

  async getProjects(): Promise<ProjectSummary[]> {
    const response = await apiClient.get('/projects');
    return response.data;
  },

  async getProject(projectId: string): Promise<Project> {
    const response = await apiClient.get(`/projects/${projectId}`);
    return response.data;
  },

  async updateSegment(projectId: string, segmentId: string, correctedText: string): Promise<SubtitleSegment> {
    const response = await apiClient.put(`/projects/${projectId}/segments/${segmentId}`, {
      correctedText
    });
    return response.data;
  },

  async applySuggestion(projectId: string, segmentId: string, suggestionId: string): Promise<void> {
    await apiClient.post(`/projects/${projectId}/segments/${segmentId}/suggestions/${suggestionId}/apply`);
  },

  async exportProject(projectId: string): Promise<Blob> {
    const response = await apiClient.post(`/projects/${projectId}/export`, {}, {
      responseType: 'blob'
    });
    return response.data;
  }
};

export const dictionaryApi = {
  async getTerms(): Promise<UserDictionaryTerm[]> {
    const response = await apiClient.get('/dictionary');
    return response.data;
  },

  async addTerm(phrase: string, caseSensitive: boolean): Promise<UserDictionaryTerm> {
    const response = await apiClient.post('/dictionary', { phrase, caseSensitive });
    return response.data;
  },

  async deleteTerm(termId: string): Promise<void> {
    await apiClient.delete(`/dictionary/${termId}`);
  }
};
```

### 6.4 Hlavní komponenty

#### 6.4.1 ProjectListPanel.tsx
```typescript
import React, { useState, useEffect } from 'react';
import { projectsApi } from '../services/api';
import { ProjectSummary } from '../services/types';

interface ProjectListPanelProps {
  onProjectSelect: (projectId: string) => void;
  selectedProjectId?: string;
}

const ProjectListPanel: React.FC<ProjectListPanelProps> = ({ 
  onProjectSelect, 
  selectedProjectId 
}) => {
  const [projects, setProjects] = useState<ProjectSummary[]>([]);
  const [loading, setLoading] = useState(false);
  const [showNewProjectModal, setShowNewProjectModal] = useState(false);

  useEffect(() => {
    fetchProjects();
    const interval = setInterval(fetchProjects, 10000); // Refresh každých 10 sekund
    return () => clearInterval(interval);
  }, []);

  const fetchProjects = async () => {
    try {
      const projectList = await projectsApi.getProjects();
      setProjects(projectList);
    } catch (error) {
      console.error('Failed to fetch projects:', error);
    }
  };

  const handleNewProject = async (youtubeUrl: string) => {
    setLoading(true);
    try {
      const newProject = await projectsApi.createProject(youtubeUrl);
      setProjects(prev => [newProject, ...prev]);
      setShowNewProjectModal(false);
      onProjectSelect(newProject.projectId);
    } catch (error) {
      console.error('Failed to create project:', error);
      // Zobraz error toast
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'processing': return '#3B82F6'; // modrá
      case 'needs_review': return '#F59E0B'; // oranžová
      case 'completed': return '#10B981'; // zelená
      case 'error': return '#EF4444'; // červená
      default: return '#6B7280'; // šedá
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'processing': return 'Zpracovává se...';
      case 'needs_review': return 'Vyžaduje kontrolu';
      case 'completed': return 'Dokončeno';
      case 'error': return 'Chyba';
      default: return 'Neznámý stav';
    }
  };

  return (
    <div className="project-list-panel">
      <div className="panel-header">
        <h2>Projekty</h2>
        <button 
          className="new-project-btn"
          onClick={() => setShowNewProjectModal(true)}
          disabled={loading}
        >
          + Nový projekt
        </button>
      </div>

      <div className="project-list">
        {projects.map(project => (
          <div
            key={project.projectId}
            className={`project-item ${selectedProjectId === project.projectId ? 'selected' : ''}`}
            onClick={() => onProjectSelect(project.projectId)}
          >
            <div className="project-title">{project.videoTitle || 'Načítá se...'}</div>
            <div 
              className="project-status"
              style={{ color: getStatusColor(project.status) }}
            >
              {getStatusText(project.status)}
            </div>
            <div className="project-date">
              {new Date(project.createdAt).toLocaleDateString('cs-CZ')}
            </div>
          </div>
        ))}
      </div>

      {showNewProjectModal && (
        <NewProjectModal
          onSubmit={handleNewProject}
          onClose={() => setShowNewProjectModal(false)}
          loading={loading}
        />
      )}
    </div>
  );
};

const NewProjectModal: React.FC<{
  onSubmit: (url: string) => void;
  onClose: () => void;
  loading: boolean;
}> = ({ onSubmit, onClose, loading }) => {
  const [url, setUrl] = useState('');

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (url.trim()) {
      onSubmit(url.trim());
    }
  };

  return (
    <div className="modal-overlay">
      <div className="modal">
        <h3>Nový projekt</h3>
        <form onSubmit={handleSubmit}>
          <input
            type="url"
            value={url}
            onChange={(e) => setUrl(e.target.value)}
            placeholder="https://www.youtube.com/watch?v=..."
            required
            disabled={loading}
          />
          <div className="modal-actions">
            <button type="button" onClick={onClose} disabled={loading}>
              Zrušit
            </button>
            <button type="submit" disabled={loading || !url.trim()}>
              {loading ? 'Vytváří se...' : 'Vytvořit'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default ProjectListPanel;
```

#### 6.4.2 SubtitleSegmentRow.tsx
```typescript
import React, { useState, useRef, useEffect } from 'react';
import { SubtitleSegment, CorrectionSuggestion } from '../services/types';

interface SubtitleSegmentRowProps {
  segment: SubtitleSegment;
  isActive: boolean;
  onSegmentClick: (segmentId: string) => void;
  onTextChange: (segmentId: string, newText: string) => void;
  onSuggestionApply: (segmentId: string, suggestionId: string) => void;
  onSuggestionReject: (segmentId: string, suggestionId: string) => void;
}

const SubtitleSegmentRow: React.FC<SubtitleSegmentRowProps> = ({
  segment,
  isActive,
  onSegmentClick,
  onTextChange,
  onSuggestionApply,
  onSuggestionReject
}) => {
  const [isEditing, setIsEditing] = useState(false);
  const [editText, setEditText] = useState(segment.correctedText);
  const textAreaRef = useRef<HTMLTextAreaElement>(null);

  useEffect(() => {
    if (isEditing && textAreaRef.current) {
      textAreaRef.current.focus();
    }
  }, [isEditing]);

  const formatTime = (timeMs: number): string => {
    const seconds = Math.floor(timeMs / 1000);
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    const ms = timeMs % 1000;
    
    return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}.${ms.toString().padStart(3, '0')}`;
  };

  const handleTextSave = () => {
    onTextChange(segment.segmentId, editText);
    setIsEditing(false);
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && e.ctrlKey) {
      handleTextSave();
    } else if (e.key === 'Escape') {
      setEditText(segment.correctedText);
      setIsEditing(false);
    }
  };

  const getStatusClassName = () => {
    switch (segment.status) {
      case 'needs_review': return 'segment-needs-review';
      case 'auto_corrected': return 'segment-auto-corrected';
      case 'user_modified': return 'segment-user-modified';
      default: return 'segment-unchanged';
    }
  };

  const renderTextDiff = () => {
    if (segment.originalText === segment.correctedText) {
      return <span>{segment.correctedText}</span>;
    }

    // Jednoduchý diff highlighting - v reálné implementaci by se použila sofistikovanější knihovna
    const changes = segment.suggestions.filter(s => s.applied);
    let result = segment.correctedText;
    
    changes.forEach(change => {
      result = result.replace(
        change.suggestedFragment,
        `<mark class="correction">${change.suggestedFragment}</mark>`
      );
    });

    return <span dangerouslySetInnerHTML={{ __html: result }} />;
  };

  return (
    <div className={`segment-row ${getStatusClassName()} ${isActive ? 'active' : ''}`}>
      <div className="segment-timing">
        <span className="start-time">{formatTime(segment.startTimeMs)}</span>
        <span className="separator">→</span>
        <span className="end-time">{formatTime(segment.endTimeMs)}</span>
      </div>

      <div className="segment-content" onClick={() => onSegmentClick(segment.segmentId)}>
        {isEditing ? (
          <textarea
            ref={textAreaRef}
            value={editText}
            onChange={(e) => setEditText(e.target.value)}
            onBlur={handleTextSave}
            onKeyDown={handleKeyPress}
            className="segment-editor"
          />
        ) : (
          <div 
            className="segment-text"
            onDoubleClick={() => setIsEditing(true)}
          >
            {renderTextDiff()}
          </div>
        )}
      </div>

      {segment.status === 'needs_review' && segment.suggestions.length > 0 && (
        <div className="segment-actions">
          <button 
            className="accept-btn"
            onClick={() => segment.suggestions.forEach(s => 
              onSuggestionApply(segment.segmentId, s.suggestionId)
            )}
            title="Přijmout všechny návrhy (Enter)"
          >
            ✓ Přijmout
          </button>
          <button 
            className="reject-btn"
            onClick={() => segment.suggestions.forEach(s => 
              onSuggestionReject(segment.segmentId, s.suggestionId)
            )}
            title="Zamítnout všechny návrhy (Esc)"
          >
            ✗ Zamítnout
          </button>
        </div>
      )}

      {segment.suggestions.length > 0 && (
        <div className="suggestions-list">
          {segment.suggestions.map(suggestion => (
            <div key={suggestion.suggestionId} className="suggestion-item">
              <span className="suggestion-type">{suggestion.type}</span>
              <span className="suggestion-confidence">{Math.round(suggestion.confidence * 100)}%</span>
              <span className="suggestion-description">{suggestion.description}</span>
              <div className="suggestion-change">
                <span className="original">"{suggestion.originalFragment}"</span>
                <span className="arrow">→</span>
                <span className="suggested">"{suggestion.suggestedFragment}"</span>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default SubtitleSegmentRow;
```

#### 6.4.3 EditorPanel.tsx
```typescript
import React, { useState, useEffect, useCallback } from 'react';
import ReactPlayer from 'react-player';
import SubtitleSegmentRow from './SubtitleSegmentRow';
import { Project, SubtitleSegment } from '../services/types';
import { projectsApi } from '../services/api';

interface EditorPanelProps {
  project: Project | null;
  onProjectUpdate: (project: Project) => void;
}

const EditorPanel: React.FC<EditorPanelProps> = ({ project, onProjectUpdate }) => {
  const [activeSegmentId, setActiveSegmentId] = useState<string | null>(null);
  const [currentTime, setCurrentTime] = useState(0);
  const [isPlaying, setIsPlaying] = useState(false);
  const playerRef = useRef<ReactPlayer>(null);

  // Keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (!project || !activeSegmentId) return;

      const activeIndex = project.segments.findIndex(s => s.segmentId === activeSegmentId);
      
      switch (e.key) {
        case 'ArrowDown':
          e.preventDefault();
          if (activeIndex < project.segments.length - 1) {
            setActiveSegmentId(project.segments[activeIndex + 1].segmentId);
          }
          break;
        case 'ArrowUp':
          e.preventDefault();
          if (activeIndex > 0) {
            setActiveSegmentId(project.segments[activeIndex - 1].segmentId);
          }
          break;
        case 'Enter':
          e.preventDefault();
          if (project.segments[activeIndex].status === 'needs_review') {
            handleAcceptAllSuggestions(activeSegmentId);
          }
          break;
        case 'Escape':
          e.preventDefault();
          if (project.segments[activeIndex].status === 'needs_review') {
            handleRejectAllSuggestions(activeSegmentId);
          }
          break;
        case ' ':
          e.preventDefault();
          setIsPlaying(!isPlaying);
          break;
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [project, activeSegmentId, isPlaying]);

  // Synchronizace přehrávače s aktivním segmentem
  useEffect(() => {
    if (activeSegmentId && project && playerRef.current) {
      const segment = project.segments.find(s => s.segmentId === activeSegmentId);
      if (segment) {
        playerRef.current.seekTo(segment.startTimeMs / 1000);
      }
    }
  }, [activeSegmentId, project]);

  // Aktualizace aktivního segmentu podle času přehrávače
  useEffect(() => {
    if (project && currentTime > 0) {
      const currentTimeMs = currentTime * 1000;
      const currentSegment = project.segments.find(
        s => s.startTimeMs <= currentTimeMs && s.endTimeMs >= currentTimeMs
      );
      
      if (currentSegment && currentSegment.segmentId !== activeSegmentId) {
        setActiveSegmentId(currentSegment.segmentId);
      }
    }
  }, [currentTime, project, activeSegmentId]);

  const handleSegmentClick = (segmentId: string) => {
    setActiveSegmentId(segmentId);
  };

  const handleTextChange = async (segmentId: string, newText: string) => {
    if (!project) return;

    try {
      const updatedSegment = await projectsApi.updateSegment(
        project.projectId,
        segmentId,
        newText
      );

      const updatedProject = {
        ...project,
        segments: project.segments.map(s => 
          s.segmentId === segmentId ? updatedSegment : s
        )
      };

      onProjectUpdate(updatedProject);
    } catch (error) {
      console.error('Failed to update segment:', error);
    }
  };

  const handleSuggestionApply = async (segmentId: string, suggestionId: string) => {
    if (!project) return;

    try {
      await projectsApi.applySuggestion(project.projectId, segmentId, suggestionId);
      
      // Refresh projekt
      const updatedProject = await projectsApi.getProject(project.projectId);
      onProjectUpdate(updatedProject);
    } catch (error) {
      console.error('Failed to apply suggestion:', error);
    }
  };

  const handleSuggestionReject = async (segmentId: string, suggestionId: string) => {
    if (!project) return;

    // Implementace zamítnutí návrhu
    // Podobně jako apply, ale označí návrh jako zamítnutý
  };

  const handleAcceptAllSuggestions = async (segmentId: string) => {
    if (!project) return;

    const segment = project.segments.find(s => s.segmentId === segmentId);
    if (segment && segment.suggestions.length > 0) {
      for (const suggestion of segment.suggestions) {
        await handleSuggestionApply(segmentId, suggestion.suggestionId);
      }
    }
  };

  const handleRejectAllSuggestions = async (segmentId: string) => {
    if (!project) return;

    const segment = project.segments.find(s => s.segmentId === segmentId);
    if (segment && segment.suggestions.length > 0) {
      for (const suggestion of segment.suggestions) {
        await handleSuggestionReject(segmentId, suggestion.suggestionId);
      }
    }
  };

  if (!project) {
    return (
      <div className="editor-panel-empty">
        <h2>Vyberte projekt pro úpravu</h2>
        <p>Klikněte na projekt v levém panelu nebo vytvořte nový.</p>
      </div>
    );
  }

  if (project.status === 'processing') {
    return (
      <div className="editor-panel-loading">
        <h2>Zpracovává se...</h2>
        <p>Video se zpracovává pomocí AI. Počkejte prosím.</p>
        <div className="loading-spinner"></div>
      </div>
    );
  }

  if (project.status === 'error') {
    return (
      <div className="editor-panel-error">
        <h2>Chyba při zpracování</h2>
        <p>{project.errorMessage || 'Neočekávaná chyba'}</p>
        <button onClick={() => window.location.reload()}>Zkusit znovu</button>
      </div>
    );
  }

  return (
    <div className="editor-panel">
      <div className="video-player-container">
        <ReactPlayer
          ref={playerRef}
          url={project.youtubeUrl}
          width="100%"
          height="300px"
          controls={true}
          playing={isPlaying}
          onPlay={() => setIsPlaying(true)}
          onPause={() => setIsPlaying(false)}
          onProgress={({ playedSeconds }) => setCurrentTime(playedSeconds)}
        />
      </div>

      <div className="segments-container">
        <div className="segments-header">
          <h3>Titulky ({project.segments.length} segmentů)</h3>
          <div className="segments-stats">
            <span className="stat">
              <span className="stat-value">{project.statistics.needsReviewCount}</span>
              <span className="stat-label">k revizi</span>
            </span>
            <span className="stat">
              <span className="stat-value">{project.statistics.autoCorrectionsCount}</span>
              <span className="stat-label">opraveno</span>
            </span>
          </div>
        </div>

        <div className="segments-list">
          {project.segments.map(segment => (
            <SubtitleSegmentRow
              key={segment.segmentId}
              segment={segment}
              isActive={activeSegmentId === segment.segmentId}
              onSegmentClick={handleSegmentClick}
              onTextChange={handleTextChange}
              onSuggestionApply={handleSuggestionApply}
              onSuggestionReject={handleSuggestionReject}
            />
          ))}
        </div>
      </div>
    </div>
  );
};

export default EditorPanel;
```

#### 6.4.4 ActionPanel.tsx
```typescript
import React, { useState, useEffect } from 'react';
import DictionaryManager from './DictionaryManager';
import { Project } from '../services/types';
import { projectsApi } from '../services/api';

interface ActionPanelProps {
  project: Project | null;
}

const ActionPanel: React.FC<ActionPanelProps> = ({ project }) => {
  const [activeTab, setActiveTab] = useState<'dictionary' | 'project'>('project');
  const [isExporting, setIsExporting] = useState(false);

  const handleExport = async () => {
    if (!project) return;

    setIsExporting(true);
    try {
      const blob = await projectsApi.exportProject(project.projectId);
      
      // Vytvoř download link
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `${project.videoTitle || 'subtitles'}.srt`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      window.URL.revokeObjectURL(url);
    } catch (error) {
      console.error('Export failed:', error);
      alert('Export se nezdařil. Zkuste to znovu.');
    } finally {
      setIsExporting(false);
    }
  };

  const canExport = project && (project.status === 'needs_review' || project.status === 'completed');

  return (
    <div className="action-panel">
      <div className="panel-tabs">
        <button
          className={`tab ${activeTab === 'project' ? 'active' : ''}`}
          onClick={() => setActiveTab('project')}
        >
          Projekt
        </button>
        <button
          className={`tab ${activeTab === 'dictionary' ? 'active' : ''}`}
          onClick={() => setActiveTab('dictionary')}
        >
          Slovník
        </button>
      </div>

      <div className="panel-content">
        {activeTab === 'project' && (
          <div className="project-info">
            {project ? (
              <>
                <h3>Informace o projektu</h3>
                <div className="project-details">
                  <div className="detail-item">
                    <label>Název:</label>
                    <span>{project.videoTitle || 'Načítá se...'}</span>
                  </div>
                  <div className="detail-item">
                    <label>Délka:</label>
                    <span>{Math.floor(project.videoDuration / 60)}:{(project.videoDuration % 60).toString().padStart(2, '0')}</span>
                  </div>
                  <div className="detail-item">
                    <label>Stav:</label>
                    <span className={`status ${project.status}`}>
                      {project.status === 'processing' && 'Zpracovává se'}
                      {project.status === 'needs_review' && 'Vyžaduje kontrolu'}
                      {project.status === 'completed' && 'Dokončeno'}
                      {project.status === 'error' && 'Chyba'}
                    </span>
                  </div>
                  <div className="detail-item">
                    <label>Vytvořeno:</label>
                    <span>{new Date(project.createdAt).toLocaleString('cs-CZ')}</span>
                  </div>
                </div>

                <div className="project-statistics">
                  <h4>Statistiky</h4>
                  <div className="stats-grid">
                    <div className="stat-item">
                      <span className="stat-number">{project.statistics.totalSegments}</span>
                      <span className="stat-label">Celkem segmentů</span>
                    </div>
                    <div className="stat-item">
                      <span className="stat-number">{project.statistics.needsReviewCount}</span>
                      <span className="stat-label">K revizi</span>
                    </div>
                    <div className="stat-item">
                      <span className="stat-number">{project.statistics.autoCorrectionsCount}</span>
                      <span className="stat-label">Auto opravy</span>
                    </div>
                    <div className="stat-item">
                      <span className="stat-number">{project.statistics.userModifiedCount}</span>
                      <span className="stat-label">Uživatelské</span>
                    </div>
                  </div>
                </div>

                <div className="project-actions">
                  <button
                    className="export-btn"
                    onClick={handleExport}
                    disabled={!canExport || isExporting}
                  >
                    {isExporting ? 'Exportuje se...' : 'Exportovat do .SRT'}
                  </button>
                  
                  {!canExport && (
                    <p className="export-note">
                      Export je dostupný až po dokončení zpracování.
                    </p>
                  )}
                </div>
              </>
            ) : (
              <div className="no-project">
                <p>Není vybrán žádný projekt.</p>
              </div>
            )}
          </div>
        )}

        {activeTab === 'dictionary' && (
          <DictionaryManager />
        )}
      </div>
    </div>
  );
};

export default ActionPanel;
```

#### 6.4.5 DictionaryManager.tsx
```typescript
import React, { useState, useEffect } from 'react';
import { dictionaryApi } from '../services/api';
import { UserDictionaryTerm } from '../services/types';

const DictionaryManager: React.FC = () => {
  const [terms, setTerms] = useState<UserDictionaryTerm[]>([]);
  const [newPhrase, setNewPhrase] = useState('');
  const [caseSensitive, setCaseSensitive] = useState(true);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    fetchTerms();
  }, []);

  const fetchTerms = async () => {
    try {
      const termList = await dictionaryApi.getTerms();
      setTerms(termList);
    } catch (error) {
      console.error('Failed to fetch dictionary terms:', error);
    }
  };

  const handleAddTerm = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!newPhrase.trim()) return;

    setLoading(true);
    try {
      const newTerm = await dictionaryApi.addTerm(newPhrase.trim(), caseSensitive);
      setTerms(prev => [...prev, newTerm]);
      setNewPhrase('');
      setCaseSensitive(true);
    } catch (error) {
      console.error('Failed to add term:', error);
      alert('Nepodařilo se přidat termín. Možná již existuje.');
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteTerm = async (termId: string) => {
    if (!confirm('Opravdu chcete smazat tento termín?')) return;

    try {
      await dictionaryApi.deleteTerm(termId);
      setTerms(prev => prev.filter(t => t.termId !== termId));
    } catch (error) {
      console.error('Failed to delete term:', error);
      alert('Nepodařilo se smazat termín.');
    }
  };

  return (
    <div className="dictionary-manager">
      <h3>Uživatelský slovník</h3>
      <p className="dictionary-description">
        Termíny v slovníku budou při korekci titulků respektovány jako vlastní jména.
      </p>

      <form onSubmit={handleAddTerm} className="add-term-form">
        <input
          type="text"
          value={newPhrase}
          onChange={(e) => setNewPhrase(e.target.value)}
          placeholder="Zadejte termín..."
          disabled={loading}
        />
        <label className="checkbox-label">
          <input
            type="checkbox"
            checked={caseSensitive}
            onChange={(e) => setCaseSensitive(e.target.checked)}
            disabled={loading}
          />
          Rozlišovat velikost písmen
        </label>
        <button type="submit" disabled={loading || !newPhrase.trim()}>
          {loading ? 'Přidává se...' : 'Přidat'}
        </button>
      </form>

      <div className="terms-list">
        {terms.length === 0 ? (
          <p className="no-terms">Žádné termíny ve slovníku.</p>
        ) : (
          terms.map(term => (
            <div key={term.termId} className="term-item">
              <span className="term-phrase">{term.phrase}</span>
              <span className="term-info">
                {term.caseSensitive ? 'Rozlišuje velikost' : 'Nerozlišuje velikost'}
              </span>
              <button
                className="delete-btn"
                onClick={() => handleDeleteTerm(term.termId)}
                title="Smazat termín"
              >
                ✗
              </button>
            </div>
          ))
        )}
      </div>
    </div>
  );
};

export default DictionaryManager;
```

## 7. Testovací specifikace

### 7.1 Unit testy (Backend)
```python
# tests/test_processing_service.py
import pytest
from unittest.mock import Mock, patch
from app.services.processing_service import process_video, apply_ai_corrections

@pytest.mark.asyncio
async def test_process_video_success():
    """Test úspěšného zpracování videa"""
    project_id = "test-project-123"
    
    with patch('app.services.processing_service.download_audio') as mock_download:
        mock_download.return_value = "/temp/test_audio.mp3"
        
        with patch('app.services.processing_service.transcribe_with_whisper') as mock_transcribe:
            mock_transcribe.return_value = "Test transcript"
            
            # Test hlavní funkce
            result = await process_video(project_id)
            
            assert result is not None
            mock_download.assert_called_once()
            mock_transcribe.assert_called_once()

@pytest.mark.asyncio
async def test_youtube_url_validation():
    """Test validace YouTube URL"""
    from app.utils.validators import validate_youtube_url
    
    valid_urls = [
        "https://www.youtube.com/watch?v=dQw4w9WgXcQ",
        "https://youtu.be/dQw4w9WgXcQ",
        "https://m.youtube.com/watch?v=dQw4w9WgXcQ"
    ]
    
    invalid_urls = [
        "https://example.com/video",
        "not-a-url",
        "https://youtube.com/invalid"
    ]
    
    for url in valid_urls:
        assert validate_youtube_url(url) == True
    
    for url in invalid_urls:
        assert validate_youtube_url(url) == False
```

### 7.2 Integration testy
```python
# tests/test_api_integration.py
import pytest
from fastapi.testclient import TestClient
from app.main import app

client = TestClient(app)

def test_create_project():
    """Test vytvoření nového projektu"""
    response = client.post("/api/v1/projects", json={
        "youtubeUrl": "https://www.youtube.com/watch?v=dQw4w9WgXcQ"
    })
    
    assert response.status_code == 201
    data = response.json()
    assert "projectId" in data
    assert data["youtubeUrl"] == "https://www.youtube.com/watch?v=dQw4w9WgXcQ"
    assert data["status"] == "processing"

def test_get_projects():
    """Test získání seznamu projektů"""
    response = client.get("/api/v1/projects")
    
    assert response.status_code == 200
    data = response.json()
    assert isinstance(data, list)

def test_dictionary_operations():
    """Test CRUD operací na slovníku"""
    # Přidej termín
    response = client.post("/api/v1/dictionary", json={
        "phrase": "Test Term",
        "caseSensitive": True
    })
    
    assert response.status_code == 201
    term_id = response.json()["termId"]
    
    # Získej termíny
    response = client.get("/api/v1/dictionary")
    assert response.status_code == 200
    terms = response.json()
    assert len(terms) > 0
    
    # Smaž termín
    response = client.delete(f"/api/v1/dictionary/{term_id}")
    assert response.status_code == 200
```

### 7.3 End-to-end testy (Frontend)
```typescript
// tests/e2e/project-workflow.spec.ts
import { test, expect } from '@playwright/test';

test('Complete project workflow', async ({ page }) => {
  await page.goto('/');
  
  // Vytvoř nový projekt
  await page.click('[data-testid="new-project-btn"]');
  await page.fill('[data-testid="youtube-url-input"]', 'https://www.youtube.com/watch?v=dQw4w9WgXcQ');
  await page.click('[data-testid="create-project-btn"]');
  
  // Počkej na zpracování
  await page.waitForSelector('[data-testid="project-status-needs-review"]', { timeout: 300000 });
  
  // Otevři projekt
  await page.click('[data-testid="project-item"]');
  
  // Ověř načtení segmentů
  await page.waitForSelector('[data-testid="segment-row"]');
  const segments = await page.$('[data-testid="segment-row"]');
  expect(segments.length).toBeGreaterThan(0);
  
  // Testuj editaci segmentu
  await page.dblclick('[data-testid="segment-text"]');
  await page.fill('[data-testid="segment-editor"]', 'Upravený text');
  await page.keyboard.press('Enter');
  
  // Testuj export
  await page.click('[data-testid="export-btn"]');
  // Ověř download
});

test('Dictionary management', async ({ page }) => {
  await page.goto('/');
  
  // Přepni na slovník
  await page.click('[data-testid="dictionary-tab"]');
  
  // Přidej termín
  await page.fill('[data-testid="new-term-input"]', 'Test Term');
  await page.click('[data-testid="add-term-btn"]');
  
  // Ověř přidání
  await page.waitForSelector('[data-testid="term-item"]');
  const termText = await page.textContent('[data-testid="term-phrase"]');
  expect(termText).toBe('Test Term');
  
  // Smaž termín
  await page.click('[data-testid="delete-term-btn"]');
  await page.click('[data-testid="confirm-delete"]');
  
  // Ověř smazání
  const terms = await page.$('[data-testid="term-item"]');
  expect(terms.length).toBe(0);
});
```

## 8. Deployment a distribuce

### 8.1 Build proces
```json
{
  "scripts": {
    "build": "npm run build:frontend && npm run build:backend",
    "build:frontend": "webpack --mode production",
    "build:backend": "pyinstaller --onefile backend/app/main.py",
    "dist": "electron-builder",
    "dist:win": "electron-builder --win",
    "dist:mac": "electron-builder --mac",
    "dist:linux": "electron-builder --linux"
  }
}
```

### 8.2 Electron Builder konfigurace
```json
{
  "build": {
    "appId": "com.helios.subtitle-corrector",
    "productName": "Helios",
    "directories": {
      "output": "dist"
    },
    "files": [
      "build/**/*",
      "backend/dist/**/*",
      "node_modules/**/*"
    ],
    "extraResources": [
      "backend/dist/main.exe"
    ],
    "win": {
      "target": "nsis",
      "icon": "assets/icon.ico"
    },
    "mac": {
      "target": "dmg",
      "icon": "assets/icon.icns"
    },
    "linux": {
      "target": "AppImage",
      "icon": "assets/icon.png"
    }
  }
}
```

### 8.3 Instalační průvodce
```
1. Stáhnout Helios installer
2. Spustit instalaci
3. První spuštění:
   - Nastavit OpenAI API key v nastavení
   - Otestovat funkcionalitu na krátkém videu
4. Uživatelský manuál dostupný v aplikaci (Help menu)
```

## 9. Monitoring a Error Handling

### 9.1. Backend Error Handling

**Kritické chyby a jejich zpracování:**

```python
# Povinné exception classes
class HeliosException(Exception):
    """Base exception pro všechny Helios chyby"""
    def __init__(self, message: str, error_code: str, details: dict = None):
        self.message = message
        self.error_code = error_code
        self.details = details or {}
        super().__init__(self.message)

class YouTubeProcessingError(HeliosException):
    """Chyby při zpracování YouTube videa"""
    pass

class OpenAIServiceError(HeliosException):
    """Chyby komunikace s OpenAI API"""
    pass

class DatabaseError(HeliosException):
    """Chyby databázových operací"""
    pass
```

**Standardní HTTP Error Response:**
```json
{
  "error": true,
  "error_code": "YOUTUBE_DOWNLOAD_FAILED",
  "message": "Nepodařilo se stáhnout video z YouTube",
  "details": {
    "youtube_url": "https://youtube.com/watch?v=xyz",
    "yt_dlp_error": "Video is private",
    "timestamp": "2025-07-14T10:30:00Z"
  },
  "request_id": "req-uuid-1234"
}
```

**Povinné retry mechanismy:**
- OpenAI API volání: 3 pokusy s exponential backoff (1s, 2s, 4s)
- YouTube download: 2 pokusy s 5s pausou
- Database operace: 2 pokusy s 1s pausou

### 9.2. Frontend Error Handling

**Toast notifikace pro uživatele:**
```typescript
interface ErrorToast {
  type: 'error' | 'warning' | 'info';
  title: string;
  message: string;
  duration: number; // ms
  actionButton?: {
    text: string;
    action: () => void;
  };
}
```

**Uživatelsky přívětivé chybové hlášky:**
- `YOUTUBE_DOWNLOAD_FAILED` → "Video nelze stáhnout. Zkontrolujte URL nebo dostupnost videa."
- `OPENAI_QUOTA_EXCEEDED` → "Dosažen limit OpenAI API. Zkuste to později."
- `NETWORK_ERROR` → "Chyba připojení. Zkontrolujte internetové připojení."

### 9.3. Logging Framework

**Backend logging (Python):**
```python
import logging
import json
from datetime import datetime

class StructuredLogger:
    def __init__(self):
        self.logger = logging.getLogger('helios')
        handler = logging.FileHandler('logs/helios.log')
        handler.setFormatter(logging.Formatter('%(message)s'))
        self.logger.addHandler(handler)
        self.logger.setLevel(logging.INFO)
    
    def log_event(self, event_type: str, data: dict):
        log_entry = {
            "timestamp": datetime.utcnow().isoformat(),
            "event_type": event_type,
            "data": data
        }
        self.logger.info(json.dumps(log_entry))
```

**Povinné log events:**
- `PROJECT_CREATED`: `{project_id, youtube_url, user_id}`
- `PROCESSING_STARTED`: `{project_id, stage}`
- `PROCESSING_COMPLETED`: `{project_id, duration_ms, segments_count}`
- `PROCESSING_FAILED`: `{project_id, error_code, error_message}`
- `EXPORT_REQUESTED`: `{project_id, format}`
- `API_CALL_OPENAI`: `{endpoint, tokens_used, response_time_ms}`

**Frontend logging (Electron):**
```javascript
// Ukládání do ~/AppData/Roaming/Helios/logs/
const winston = require('winston');

const logger = winston.createLogger({
  level: 'info',
  format: winston.format.json(),
  transports: [
    new winston.transports.File({ filename: 'logs/error.log', level: 'error' }),
    new winston.transports.File({ filename: 'logs/combined.log' })
  ]
});
```

## 10. Bezpečnost a Validace

### 10.1. Input Validation

**YouTube URL validace:**
```python
import re
from urllib.parse import urlparse

def validate_youtube_url(url: str) -> bool:
    """Validuje YouTube URL a vrací True/False"""
    patterns = [
        r'^https?://(?:www\.)?youtube\.com/watch\?v=[\w-]+',
        r'^https?://youtu\.be/[\w-]+',
        r'^https?://(?:www\.)?youtube\.com/embed/[\w-]+'
    ]
    return any(re.match(pattern, url) for pattern in patterns)

def sanitize_youtube_url(url: str) -> str:
    """Čistí URL od tracking parametrů"""
    # Implementace sanitizace
    pass
```

**API Input Validation:**
```python
from pydantic import BaseModel, HttpUrl, validator

class CreateProjectRequest(BaseModel):
    youtubeUrl: HttpUrl
    
    @validator('youtubeUrl')
    def validate_youtube_url(cls, v):
        if not validate_youtube_url(str(v)):
            raise ValueError('Invalid YouTube URL')
        return v

class UpdateSegmentRequest(BaseModel):
    correctedText: str
    
    @validator('correctedText')
    def validate_text_length(cls, v):
        if len(v) > 1000:
            raise ValueError('Text too long (max 1000 characters)')
        return v
```

### 10.2. API Security

**Rate Limiting:**
```python
from fastapi import FastAPI, Request
from slowapi import Limiter, _rate_limit_exceeded_handler
from slowapi.util import get_remote_address
from slowapi.errors import RateLimitExceeded

limiter = Limiter(key_func=get_remote_address)
app = FastAPI()
app.state.limiter = limiter
app.add_exception_handler(RateLimitExceeded, _rate_limit_exceeded_handler)

@app.post("/api/v1/projects")
@limiter.limit("5/minute")  # Max 5 projektů za minutu
async def create_project(request: Request, data: CreateProjectRequest):
    pass
```

**API Key Management:**
```python
import os
from cryptography.fernet import Fernet

class SecureConfig:
    def __init__(self):
        self.encryption_key = os.environ.get('HELIOS_ENCRYPTION_KEY')
        if not self.encryption_key:
            raise ValueError("Missing HELIOS_ENCRYPTION_KEY environment variable")
        self.fernet = Fernet(self.encryption_key)
    
    def get_openai_key(self) -> str:
        encrypted_key = os.environ.get('OPENAI_API_KEY_ENCRYPTED')
        return self.fernet.decrypt(encrypted_key.encode()).decode()
```

### 10.3. Data Privacy

**Lokální data storage:**
- Všechna stažená audio/video soubory se ukládají do `~/AppData/Local/Helios/temp/`
- Soubory se automaticky mažou po dokončení zpracování
- Databáze se ukládá do `~/AppData/Roaming/Helios/helios.db`

**API komunikace:**
- Pouze text titulků se posílá na OpenAI API
- Audio se posílá na Whisper API pouze pro transkripci
- Žádné video soubory se neposílají externím službám

## 11. Konfigurace a Nastavení

### 11.1. Konfigurační soubor

**config.json struktura:**
```json
{
  "app": {
    "version": "1.0.0",
    "environment": "production",
    "log_level": "INFO"
  },
  "api": {
    "base_url": "http://localhost:8000",
    "timeout": 30000,
    "retry_count": 3
  },
  "openai": {
    "model_whisper": "whisper-1",
    "model_gpt": "gpt-4-turbo",
    "max_tokens": 4000,
    "temperature": 0.1
  },
  "processing": {
    "max_concurrent_projects": 3,
    "temp_dir": "~/AppData/Local/Helios/temp/",
    "auto_cleanup": true
  },
  "ui": {
    "theme": "dark",
    "auto_refresh_interval": 10000,
    "video_player_volume": 0.7
  }
}
```

### 11.2. Environment Variables

**Povinné proměnné:**
```bash
# Backend
HELIOS_ENCRYPTION_KEY=base64-encoded-key
OPENAI_API_KEY_ENCRYPTED=encrypted-api-key
DATABASE_URL=sqlite:///helios.db

# Frontend
REACT_APP_API_URL=http://localhost:8000
REACT_APP_ENVIRONMENT=production
```

**Volitelné proměnné:**
```bash
HELIOS_LOG_LEVEL=INFO
HELIOS_MAX_PROJECTS=10
HELIOS_TEMP_DIR=/custom/temp/path
```

## 12. Lokalizace

### 12.1. I18n Framework

**Frontend lokalizace (React):**
```javascript
// src/i18n/cs.json
{
  "project": {
    "status": {
      "processing": "Zpracovává se...",
      "needs_review": "Vyžaduje kontrolu",
      "completed": "Dokončeno"
    },
    "actions": {
      "new_project": "Nový projekt",
      "export_srt": "Exportovat do .SRT",
      "accept": "Přijmout",
      "reject": "Zamítnout"
    }
  },
  "errors": {
    "youtube_download_failed": "Video nelze stáhnout. Zkontrolujte URL nebo dostupnost videa.",
    "network_error": "Chyba připojení. Zkontrolujte internetové připojení."
  }
}
```

**Backend lokalizace pro LLM prompty:**
```python
class PromptTemplate:
    CZECH_CORRECTION_PROMPT = """
    Jsi expert na korekturu českých titulků. Tvým úkolem je porovnat "Původní text" 
    s "Referenčním textem" a vygenerovat seznam oprav ve formátu JSON.
    
    PRAVIDLA:
    1. Opravuj pouze chyby v interpunkci, velkých písmenech a zjevné překlepy.
    2. ZÁSADNÍ PRAVIDLO: Následující termíny z "Uživatelského slovníku" jsou vlastní jména...
    """
```

## 13. Monitoring a Telemetrie

### 13.1. Application Metrics

**Backend metriky:**
```python
from prometheus_client import Counter, Histogram, Gauge
import time

# Metriky
projects_created = Counter('helios_projects_created_total', 'Total created projects')
processing_duration = Histogram('helios_processing_duration_seconds', 'Processing duration')
active_projects = Gauge('helios_active_projects', 'Currently active projects')
openai_api_calls = Counter('helios_openai_calls_total', 'OpenAI API calls', ['endpoint', 'status'])

# Použití
def create_project(youtube_url: str):
    projects_created.inc()
    start_time = time.time()
    try:
        # Processing logic
        pass
    finally:
        processing_duration.observe(time.time() - start_time)
```

**Frontend analytics (anonymní):**
```javascript
class Analytics {
  track(event, properties = {}) {
    const payload = {
      event,
      properties: {
        ...properties,
        timestamp: new Date().toISOString(),
        app_version: app.getVersion(),
        platform: process.platform
      }
    };
    
    // Ukládání do lokálního souboru pro pozdější batch upload
    this.storeLocally(payload);
  }
}

// Trackované události
analytics.track('project_created', { youtube_domain: 'youtube.com' });
analytics.track('segment_manually_edited', { segment_length: 45 });
analytics.track('export_completed', { segments_count: 150 });
```

### 13.2. Health Checks

**Backend health endpoint:**
```python
@app.get("/health")
async def health_check():
    return {
        "status": "healthy",
        "timestamp": datetime.utcnow().isoformat(),
        "version": "1.0.0",
        "services": {
            "database": await check_database_connection(),
            "openai": await check_openai_connection(),
            "disk_space": check_disk_space()
        }
    }
```

## 14. Upgrade a Migrace

### 14.1. Database Migrations

**Alembic konfigurace:**
```python
# alembic/versions/001_initial_schema.py
from alembic import op
import sqlalchemy as sa

def upgrade():
    op.create_table(
        'projects',
        sa.Column('id', sa.String(), primary_key=True),
        sa.Column('youtube_url', sa.String(), nullable=False),
        sa.Column('status', sa.String(), nullable=False),
        sa.Column('created_at', sa.DateTime(), nullable=False),
        sa.Column('updated_at', sa.DateTime(), nullable=False)
    )

def downgrade():
    op.drop_table('projects')
```

### 14.2. App Auto-Update

**Frontend auto-update (Electron):**
```javascript
const { autoUpdater } = require('electron-updater');

class UpdateManager {
  constructor() {
    autoUpdater.checkForUpdatesAndNotify();
    
    autoUpdater.on('update-available', () => {
      dialog.showMessageBox({
        type: 'info',
        title: 'Dostupná aktualizace',
        message: 'Nová verze Helios je dostupná. Bude stažena na pozadí.',
        buttons: ['OK']
      });
    });
    
    autoUpdater.on('update-downloaded', () => {
      dialog.showMessageBox({
        type: 'info',
        title: 'Aktualizace připravena',
        message: 'Aktualizace byla stažena. Restartujte aplikaci pro instalaci.',
        buttons: ['Restartovat', 'Později']
      }).then((result) => {
        if (result.response === 0) {
          autoUpdater.quitAndInstall();
        }
      });
    });
  }
}
```

## 15. Dokumentace a Onboarding

### 15.1. User Documentation

**In-app help systém:**
```javascript
// src/components/HelpSystem.jsx
const HelpTooltip = ({ target, content }) => {
  return (
    <Tooltip target={target} placement="bottom">
      <div className="help-content">
        <h4>{content.title}</h4>
        <p>{content.description}</p>
        {content.videoUrl && (
          <a href={content.videoUrl} target="_blank">
            Zobrazit video návod
          </a>
        )}
      </div>
    </Tooltip>
  );
};
```

**Onboarding tour:**
```javascript
class OnboardingTour {
  constructor() {
    this.steps = [
      {
        target: '#new-project-btn',
        title: 'Vytvoření nového projektu',
        content: 'Klikněte zde pro vytvoření nového projektu z YouTube URL.'
      },
      {
        target: '#project-list',
        title: 'Seznam projektů',
        content: 'Zde vidíte všechny své projekty a jejich aktuální stav.'
      },
      {
        target: '#editor-panel',
        title: 'Editor titulků',
        content: 'Zde upravujete a kontrolujete automaticky opravené titulky.'
      }
    ];
  }
  
  start() {
    if (this.isFirstRun()) {
      this.showStep(0);
    }
  }
}
```

### 15.2. Developer Documentation

**API dokumentace (automatická):**
```python
from fastapi import FastAPI
from fastapi.openapi.utils import get_openapi

def custom_openapi():
    if app.openapi_schema:
        return app.openapi_schema
    
    openapi_schema = get_openapi(
        title="Helios API",
        version="1.0.0",
        description="API pro AI korektor titulků Helios",
        routes=app.routes,
    )
    
    app.openapi_schema = openapi_schema
    return app.openapi_schema

app.openapi = custom_openapi
```

**Code examples pro rozšíření:**
```python
# examples/custom_correction_rule.py
from helios.correction import CorrectionRule

class CustomRule(CorrectionRule):
    def applies_to(self, segment: SubtitleSegment) -> bool:
        return "specifický pattern" in segment.original_text
    
    def suggest_correction(self, segment: SubtitleSegment) -> CorrectionSuggestion:
        return CorrectionSuggestion(
            type="custom_rule",
            confidence=0.85,
            original_fragment="original",
            suggested_fragment="corrected",
            description="Vlastní korekční pravidlo"
        )
```

## 16. Testování

### 16.1 Backend testy (pytest)

#### 16.1.1 Testovací konfigurace (conftest.py)
```python
import pytest
from fastapi.testclient import TestClient
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from backend.main import app
from backend.database import get_db, Base

# Testovací databáze
SQLALCHEMY_DATABASE_URL = "sqlite:///./test.db"
engine = create_engine(SQLALCHEMY_DATABASE_URL, connect_args={"check_same_thread": False})
TestingSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

def override_get_db():
    try:
        db = TestingSessionLocal()
        yield db
    finally:
        db.close()

app.dependency_overrides[get_db] = override_get_db

@pytest.fixture
def client():
    Base.metadata.create_all(bind=engine)
    with TestClient(app) as c:
        yield c
    Base.metadata.drop_all(bind=engine)
```

#### 16.1.2 API testy (test_api.py)
```python
def test_create_project(client):
    """Test vytvoření nového projektu"""
    response = client.post(
        "/api/v1/projects/",
        json={"youtubeUrl": "https://www.youtube.com/watch?v=test123"}
    )
    assert response.status_code == 200
    data = response.json()
    assert "projectId" in data
    assert data["status"] == "PENDING"

def test_get_projects(client):
    """Test získání seznamu projektů"""
    response = client.get("/api/v1/projects/")
    assert response.status_code == 200
    assert isinstance(response.json(), list)

def test_invalid_youtube_url(client):
    """Test neplatné YouTube URL"""
    response = client.post(
        "/api/v1/projects/",
        json={"youtubeUrl": "https://example.com/invalid"}
    )
    assert response.status_code == 400
```

#### 16.1.3 Služby testy (test_services.py)
```python
def test_youtube_service_validation():
    """Test validace YouTube URL"""
    from backend.services import YouTubeService

    valid_urls = [
        "https://www.youtube.com/watch?v=dQw4w9WgXcQ",
        "https://youtu.be/dQw4w9WgXcQ",
        "https://youtube.com/embed/dQw4w9WgXcQ"
    ]

    invalid_urls = [
        "https://example.com",
        "not-a-url",
        "https://vimeo.com/123456"
    ]

    for url in valid_urls:
        assert YouTubeService.is_valid_youtube_url(url)

    for url in invalid_urls:
        assert not YouTubeService.is_valid_youtube_url(url)
```

### 16.2 Spuštění testů
```bash
# Backend testy
cd backend
pytest tests/ -v

# S pokrytím kódu
pytest tests/ --cov=backend --cov-report=html

# Frontend testy (pokud budou implementovány)
cd frontend
npm test
```

## 17. Deployment

### 17.1 Docker konfigurace

#### 17.1.1 Backend Dockerfile
```dockerfile
FROM python:3.11-slim

WORKDIR /app

# Instalace systémových závislostí
RUN apt-get update && apt-get install -y \
    ffmpeg \
    && rm -rf /var/lib/apt/lists/*

# Kopírování a instalace Python závislostí
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Kopírování aplikace
COPY . .

# Vytvoření temp adresáře
RUN mkdir -p ./temp

EXPOSE 8000

CMD ["uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8000"]
```

#### 17.1.2 Docker Compose
```yaml
version: '3.8'

services:
  backend:
    build: ./backend
    ports:
      - "8000:8000"
    environment:
      - DATABASE_URL=sqlite:///./ai_korektor.db
      - OPENAI_API_KEY=${OPENAI_API_KEY}
    volumes:
      - ./data:/app/data
      - ./temp:/app/temp
    restart: unless-stopped

  frontend:
    build: ./frontend
    ports:
      - "3000:3000"
    depends_on:
      - backend
    restart: unless-stopped
```

### 17.2 Produkční nasazení
```bash
# 1. Klonování repozitáře
git clone https://github.com/your-repo/ai-korektor-titulku.git
cd ai-korektor-titulku

# 2. Konfigurace prostředí
cp .env.example .env
# Edituj .env s produkčními hodnotami

# 3. Spuštění pomocí Docker Compose
docker-compose up -d

# 4. Ověření běhu
curl http://localhost:8000/health
```

## 18. Shrnutí a další kroky

### 18.1 Implementované funkce
- ✅ Automatické stahování a zpracování YouTube videí
- ✅ AI-powered korekce titulků pomocí OpenAI GPT
- ✅ Webové rozhraní pro editaci titulků
- ✅ Export do SRT formátu
- ✅ Uživatelský slovník pro vlastní korekce
- ✅ RESTful API s kompletní dokumentací
- ✅ Databázové úložiště projektů a segmentů

### 18.2 Technické specifikace
- **Backend**: FastAPI + SQLAlchemy + OpenAI + yt-dlp
- **Frontend**: React + TypeScript + Electron
- **Databáze**: SQLite (možnost rozšíření na PostgreSQL)
- **AI**: OpenAI GPT-4o-mini pro korekce textu
- **Deployment**: Docker + Docker Compose

### 18.3 Nově implementované funkce (2025-07-15)
- ✅ **Pokročilý Diff View**: Vizuální porovnání změn s word-level přesností
- ✅ **Suggestion System UI**: Kompletní správa AI návrhů s batch operacemi
- ✅ **Klávesové zkratky**: Plná podpora navigace a editace pomocí klávesnice
- ✅ **Performance monitoring**: Real-time metriky a system resource tracking
- ✅ **Pokročilý export**: Podpora SRT, VTT, JSON, CSV, TXT formátů s validací
- ✅ **Retry mechanismus**: Robustní error handling pro všechny API volání
- ✅ **Advanced export options**: Výběr segmentů, kódování, preview funkcionalita

### 18.4 Další možná rozšíření
- 🔄 Podpora dalších video platforem (Vimeo, atd.)
- 🔄 Batch zpracování více videí najednou
- 🔄 Pokročilé AI modely pro specifické domény
- 🔄 Kolaborativní editace titulků
- 🔄 Integrace s profesionálními video editory
- 🔄 Podpora více jazyků
- 🔄 Automatické rozpoznání jazyka
- 🔄 Pokročilé statistiky a reporty

### 18.5 Kontakt a podpora
Pro technické dotazy a podporu kontaktujte vývojový tým nebo vytvořte issue v GitHub repozitáři.

## 19. Implementační Status (Červenec 2025)

### 19.1 ✅ Dokončené funkce

#### Core Funkcionalita
- ✅ **Projekt Management**: Vytváření, správa a seznam projektů
- ✅ **YouTube Integration**: Stahování videí a extrakce audio
- ✅ **AI Transkripce**: OpenAI Whisper API integrace
- ✅ **Segment Management**: Editace, navigace a správa segmentů
- ✅ **Real-time Preview**: Synchronizace videa s titulky

#### AI Suggestion System
- ✅ **Pokročilé AI návrhy**: Gramatické, sémantické a stylistické korekce
- ✅ **Batch operace**: Hromadné přijímání/zamítání návrhů
- ✅ **Filtrování návrhů**: Podle typu a confidence skóre
- ✅ **Interaktivní aplikace**: Jednotlivé i hromadné aplikace návrhů

#### User Experience
- ✅ **Klávesové zkratky**: Kompletní sada pro produktivní práci
- ✅ **Responsivní UI**: Moderní React interface s TypeScript
- ✅ **Progress tracking**: Real-time sledování zpracování
- ✅ **Error handling**: Robustní zpracování chyb

#### Technical Infrastructure
- ✅ **Port Management**: Konfigurované porty v .env souboru
- ✅ **Service Scripts**: start.sh a stop.sh pro správu služeb
- ✅ **Database**: SQLite s kompletními modely
- ✅ **API Documentation**: FastAPI automatická dokumentace

### 19.2 🔧 Technické detaily

#### Porty a konfigurace
- **Backend**: http://localhost:8001 (konfigurovatelný v .env)
- **Frontend**: http://localhost:3000 (konfigurovatelný v .env)
- **Database**: SQLite v backend/app.db
- **Logs**: logs/backend.log, logs/frontend.log

#### Spuštění aplikace
```bash
# Spuštění všech služeb
./start.sh

# Zastavení všech služeb
./stop.sh
```

#### API Endpointy
- `GET /api/v1/projects/` - Seznam projektů
- `POST /api/v1/projects/` - Vytvoření projektu
- `GET /api/v1/projects/{id}` - Detail projektu
- `PUT /api/v1/projects/{id}/segments/{id}` - Editace segmentu
- `POST /api/v1/projects/{id}/segments/{id}/suggestions/{id}/apply` - Aplikace návrhu

#### Klávesové zkratky
- **Mezerník**: Přehrát/Pozastavit
- **↑/↓**: Navigace mezi segmenty
- **F2**: Editace segmentu
- **Enter**: Přijmout návrhy
- **Esc**: Zamítnout návrhy
- **Ctrl+S**: Export
- **Ctrl+F**: Celá obrazovka
- **1-9**: Přejít na segment

### 19.3 📊 Testování a validace

#### Provedené testy
- ✅ **API testování**: Všechny endpointy funkční
- ✅ **Frontend kompilace**: Bez chyb a warningů
- ✅ **Service management**: Start/stop skripty funkční
- ✅ **Port management**: Automatické řešení konfliktů
- ✅ **Database**: Správné vytvoření tabulek a vztahů

#### Testovací scénáře
- ✅ Vytvoření projektu s YouTube URL
- ✅ Načítání seznamu projektů
- ✅ Navigace v UI bez chyb
- ✅ Správa portů a služeb

---

**Dokument vytvořen**: Prosinec 2024
**Poslední aktualizace**: 16. července 2025
**Verze**: 2.1
**Status**: ✅ **KOMPLETNĚ IMPLEMENTOVÁNO** - Všechny funkce podle PRD jsou hotové a testované

