import React from 'react';
interface ComparisonSegment {
    segment_id: string;
    sequence_number: number;
    start_time_ms: number;
    end_time_ms: number;
    original_text: string;
    corrected_text: string;
    confidence_score?: number;
    status: string;
}
interface SubtitleComparisonProps {
    projectId: string;
    onClose: () => void;
    onApplyChanges: (sourceId: string, segments: ComparisonSegment[]) => void;
}
declare const SubtitleComparison: React.FC<SubtitleComparisonProps>;
export default SubtitleComparison;
