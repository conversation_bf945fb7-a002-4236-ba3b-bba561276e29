import React from 'react';
interface SubtitleSource {
    source_id: string;
    source_type: string;
    source_name: string;
    language: string;
    total_segments: number;
    status: string;
    created_at: string;
    updated_at: string;
}
interface SubtitleSourceManagerProps {
    projectId: string;
    onSourceSelect: (sourceId: string, sourceType: string) => void;
    onSourceComparison: (sources: SubtitleSource[]) => void;
    selectedSourceId?: string;
}
declare const SubtitleSourceManager: React.FC<SubtitleSourceManagerProps>;
export default SubtitleSourceManager;
