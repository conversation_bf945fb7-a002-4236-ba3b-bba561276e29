import { Project, ProjectSummary, SubtitleSegment, UserDictionaryTerm } from '../types';
declare const apiClient: import("axios").AxiosInstance;
export declare const projectsApi: {
    createProject(youtubeUrl: string): Promise<Project>;
    getProjects(): Promise<ProjectSummary[]>;
    getProject(projectId: string): Promise<Project>;
    updateSegment(projectId: string, segmentId: string, correctedText: string): Promise<SubtitleSegment>;
    applySuggestion(projectId: string, segmentId: string, suggestionId: string): Promise<void>;
    rejectSuggestion(projectId: string, segmentId: string, suggestionId: string): Promise<void>;
    getExportPreview(projectId: string): Promise<any>;
    exportProject(projectId: string): Promise<Blob>;
    getAvailableSubtitles(projectId: string): Promise<any>;
    extractSubtitles(projectId: string, language?: string): Promise<any>;
    downloadAudio(projectId: string): Promise<any>;
    transcribeWithWhisper(projectId: string): Promise<any>;
    correctWithAI(projectId: string): Promise<any>;
    deleteProject(projectId: string): Promise<void>;
    getProjectSubtitleSources(projectId: string): Promise<{
        project_id: string;
        sources: Array<{
            source_id: string;
            source_type: string;
            source_name: string;
            language: string;
            total_segments: number;
            status: string;
            created_at: string;
            updated_at: string;
        }>;
    }>;
    createYouTubeSubtitleSource(projectId: string, language?: string): Promise<{
        source_id: string;
        source_type: string;
        source_name: string;
        language: string;
        total_segments: number;
        status: string;
        created_at: string;
    }>;
    createWhisperSubtitleSource(projectId: string, audioFilePath: string): Promise<{
        source_id: string;
        source_type: string;
        source_name: string;
        language: string;
        total_segments: number;
        status: string;
        created_at: string;
    }>;
    getSourceSegments(projectId: string, sourceId: string): Promise<{
        source_id: string;
        segments: Array<{
            segment_id: string;
            sequence_number: number;
            start_time_ms: number;
            end_time_ms: number;
            original_text: string;
            corrected_text: string;
            confidence_score?: number;
            status: string;
        }>;
    }>;
    getSourcesComparison(projectId: string): Promise<{
        project_id: string;
        sources: Array<{
            source_id: string;
            source_type: string;
            source_name: string;
            language: string;
            total_segments: number;
            status: string;
            segments: Array<{
                segment_id: string;
                sequence_number: number;
                start_time_ms: number;
                end_time_ms: number;
                original_text: string;
                corrected_text: string;
                confidence_score?: number;
                status: string;
            }>;
        }>;
    }>;
    deleteSubtitleSource(projectId: string, sourceId: string): Promise<{
        message: string;
    }>;
};
export declare const dictionaryApi: {
    getTerms(): Promise<UserDictionaryTerm[]>;
    addTerm(phrase: string, caseSensitive: boolean): Promise<UserDictionaryTerm>;
    deleteTerm(termId: string): Promise<void>;
};
export default apiClient;
