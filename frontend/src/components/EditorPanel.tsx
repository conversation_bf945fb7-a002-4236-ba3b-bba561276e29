import React, { useState, useEffect, useRef } from 'react';
import ReactPlayer from 'react-player';
import { useProjectContext } from '../contexts/ProjectContext';
import SubtitleSegmentRow from './SubtitleSegmentRow';
import SubtitleSourceManager from './SubtitleSourceManager';
import SubtitleComparison from './SubtitleComparison';
import { projectsApi } from '../services/api';
import { useKeyboardShortcuts } from '../hooks/useKeyboardShortcuts';
import { Play, Pause, AlertCircle, Loader2, GitCompare, Layers, X } from 'lucide-react';

const EditorPanel: React.FC = () => {
  const { state, updateSegment, refreshCurrentProject } = useProjectContext();
  const [activeSegmentId, setActiveSegmentId] = useState<string | null>(null);
  const [currentTime, setCurrentTime] = useState(0);
  const [isPlaying, setIsPlaying] = useState(false);
  const [userClickedSegment, setUserClickedSegment] = useState(false);
  const [isEditingSegment, setIsEditingSegment] = useState(false);
  const playerRef = useRef<ReactPlayer>(null);

  // Nové stavy pro správu zdrojů
  const [showSourceManager, setShowSourceManager] = useState(false);
  const [showComparison, setShowComparison] = useState(false);
  const [currentSourceId, setCurrentSourceId] = useState<string | null>(null);
  const [currentSourceType, setCurrentSourceType] = useState<string | null>(null);

  // Keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (!state.currentProject || !activeSegmentId) return;

      const activeIndex = state.currentProject.segments.findIndex(
        (s) => s.segment_id === activeSegmentId
      );

      switch (e.key) {
        case 'ArrowDown':
          e.preventDefault();
          if (activeIndex < state.currentProject.segments.length - 1) {
            setActiveSegmentId(
              state.currentProject.segments[activeIndex + 1].segment_id
            );
          }
          break;
        case 'ArrowUp':
          e.preventDefault();
          if (activeIndex > 0) {
            setActiveSegmentId(
              state.currentProject.segments[activeIndex - 1].segment_id
            );
          }
          break;
        case ' ':
          e.preventDefault();
          setIsPlaying(!isPlaying);
          break;
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [state.currentProject, activeSegmentId, isPlaying]);

  // Sync player with active segment (only when user clicks)
  useEffect(() => {
    if (activeSegmentId && state.currentProject && playerRef.current && userClickedSegment) {
      const segment = state.currentProject.segments.find(
        (s) => s.segment_id === activeSegmentId
      );
      if (segment) {
        playerRef.current.seekTo(segment.start_time_ms / 1000);
        // Reset flag after seeking
        setTimeout(() => setUserClickedSegment(false), 100);
      }
    }
  }, [activeSegmentId, state.currentProject, userClickedSegment]);

  // Update active segment based on player time (only when not user-initiated)
  useEffect(() => {
    if (state.currentProject && currentTime > 0 && !userClickedSegment) {
      const currentTimeMs = currentTime * 1000;
      const currentSegment = state.currentProject.segments.find(
        (s) => s.start_time_ms <= currentTimeMs && s.end_time_ms >= currentTimeMs
      );

      if (currentSegment && currentSegment.segment_id !== activeSegmentId) {
        setActiveSegmentId(currentSegment.segment_id);
      }
    }
  }, [currentTime, state.currentProject, activeSegmentId, userClickedSegment]);

  const handleSegmentClick = (segmentId: string) => {
    setUserClickedSegment(true);
    setActiveSegmentId(segmentId);
  };

  const handleTextChange = async (segmentId: string, newText: string) => {
    try {
      await updateSegment(segmentId, newText);
    } catch (error) {
      console.error('Failed to update segment:', error);
    }
  };

  const handleApplySuggestion = async (segmentId: string, suggestionId: string) => {
    try {
      if (!state.currentProject) return;

      await projectsApi.applySuggestion(
        state.currentProject.project_id,
        segmentId,
        suggestionId
      );

      // Refresh project to get updated data
      await refreshCurrentProject();
    } catch (error) {
      console.error('Failed to apply suggestion:', error);
      alert('Nepodařilo se aplikovat návrh');
    }
  };

  const handleRejectSuggestion = async (segmentId: string, suggestionId: string) => {
    try {
      if (!state.currentProject) return;

      await projectsApi.rejectSuggestion(
        state.currentProject.project_id,
        segmentId,
        suggestionId
      );

      // Refresh project to get updated data
      await refreshCurrentProject();
    } catch (error) {
      console.error('Failed to reject suggestion:', error);
      alert('Nepodařilo se zamítnout návrh');
    }
  };

  // Navigation functions for keyboard shortcuts
  const handleNextSegment = () => {
    if (!state.currentProject?.segments || !activeSegmentId) return;

    const currentIndex = state.currentProject.segments.findIndex(
      s => s.segment_id === activeSegmentId
    );

    if (currentIndex < state.currentProject.segments.length - 1) {
      const nextSegment = state.currentProject.segments[currentIndex + 1];
      setActiveSegmentId(nextSegment.segment_id);
      setUserClickedSegment(true);
    }
  };

  const handlePreviousSegment = () => {
    if (!state.currentProject?.segments || !activeSegmentId) return;

    const currentIndex = state.currentProject.segments.findIndex(
      s => s.segment_id === activeSegmentId
    );

    if (currentIndex > 0) {
      const prevSegment = state.currentProject.segments[currentIndex - 1];
      setActiveSegmentId(prevSegment.segment_id);
      setUserClickedSegment(true);
    }
  };

  const handlePlayPause = () => {
    setIsPlaying(!isPlaying);
  };

  const handleEditCurrentSegment = () => {
    if (activeSegmentId) {
      setIsEditingSegment(true);
    }
  };

  const handleEditingChange = (isEditing: boolean) => {
    setIsEditingSegment(isEditing);
  };

  const handleAcceptCurrentSuggestions = async () => {
    if (!activeSegmentId || !state.currentProject) return;

    const activeSegment = state.currentProject.segments.find(
      s => s.segment_id === activeSegmentId
    );

    if (activeSegment?.suggestions) {
      for (const suggestion of activeSegment.suggestions) {
        if (!suggestion.applied) {
          await handleApplySuggestion(activeSegmentId, suggestion.suggestion_id);
        }
      }
    }
  };

  const handleRejectCurrentSuggestions = async () => {
    if (!activeSegmentId || !state.currentProject) return;

    const activeSegment = state.currentProject.segments.find(
      s => s.segment_id === activeSegmentId
    );

    if (activeSegment?.suggestions) {
      for (const suggestion of activeSegment.suggestions) {
        if (!suggestion.applied) {
          await handleRejectSuggestion(activeSegmentId, suggestion.suggestion_id);
        }
      }
    }
  };

  const formatDuration = (seconds: number): string => {
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const handleJumpToSegment = (index: number) => {
    if (state.currentProject && state.currentProject.segments[index]) {
      const segment = state.currentProject.segments[index];
      setActiveSegmentId(segment.segment_id);
      // Jump to segment time in video
      if (playerRef.current) {
        playerRef.current.seekTo(segment.start_time_ms / 1000, 'seconds');
      }
    }
  };

  const handleSeekForward = () => {
    if (playerRef.current) {
      const currentTime = playerRef.current.getCurrentTime();
      playerRef.current.seekTo(currentTime + 5, 'seconds'); // Seek 5 seconds forward
    }
  };

  const handleSeekBackward = () => {
    if (playerRef.current) {
      const currentTime = playerRef.current.getCurrentTime();
      playerRef.current.seekTo(Math.max(0, currentTime - 5), 'seconds'); // Seek 5 seconds backward
    }
  };

  const handleToggleFullscreen = () => {
    if (document.fullscreenElement) {
      document.exitFullscreen();
    } else {
      document.documentElement.requestFullscreen();
    }
  };

  const handleExport = () => {
    // Trigger export functionality
    if (state.currentProject) {
      // This would typically open an export dialog or start export
      console.log('Export triggered via keyboard shortcut');
      // You could implement export logic here or trigger an export modal
    }
  };

  const handleHelp = () => {
    // Show help dialog or keyboard shortcuts
    alert(`Klávesové zkratky:

Přehrávání:
• Mezerník - Přehrát/Pozastavit
• Ctrl+← - Přetočit zpět (5s)
• Ctrl+→ - Přetočit vpřed (5s)

Navigace:
• ↑ - Předchozí segment
• ↓ - Následující segment
• 1-9 - Přejít na segment

Editace:
• F2 - Editovat segment
• Enter - Přijmout návrhy
• Esc - Zamítnout návrhy
• Ctrl+Enter - Uložit editaci
• Ctrl+Esc - Zrušit editaci

Ostatní:
• Ctrl+S - Export
• Ctrl+F - Celá obrazovka
• Ctrl+H nebo ? - Nápověda`);
  };

  // Setup keyboard shortcuts
  useKeyboardShortcuts({
    onPlayPause: handlePlayPause,
    onNextSegment: handleNextSegment,
    onPreviousSegment: handlePreviousSegment,
    onEditSegment: handleEditCurrentSegment,
    onAcceptSuggestions: handleAcceptCurrentSuggestions,
    onRejectSuggestions: handleRejectCurrentSuggestions,
    onJumpToSegment: handleJumpToSegment,
    onSeekForward: handleSeekForward,
    onSeekBackward: handleSeekBackward,
    onToggleFullscreen: handleToggleFullscreen,
    onExport: handleExport,
    onHelp: handleHelp,
    isEditing: isEditingSegment,
    disabled: !state.currentProject || state.currentProject.status === 'processing',
  });

  // Funkce pro správu zdrojů titulků
  const handleSourceSelect = async (sourceId: string, sourceType: string) => {
    try {
      if (!state.currentProject) return;

      // Načti segmenty z vybraného zdroje
      const sourceData = await projectsApi.getSourceSegments(state.currentProject.project_id, sourceId);

      // Aktualizuj stav
      setCurrentSourceId(sourceId);
      setCurrentSourceType(sourceType);
      setShowSourceManager(false);

      // Refresh projektu pro načtení nových segmentů
      await refreshCurrentProject();
    } catch (error) {
      console.error('Error selecting source:', error);
    }
  };

  const handleSourceComparison = (sources: any[]) => {
    setShowComparison(true);
    setShowSourceManager(false);
  };

  const handleApplyComparisonChanges = async (sourceId: string, segments: any[]) => {
    try {
      if (!state.currentProject) return;

      // TODO: Implementovat aplikaci změn z porovnání
      console.log('Applying changes from comparison:', { sourceId, segments });

      setShowComparison(false);
      await refreshCurrentProject();
    } catch (error) {
      console.error('Error applying comparison changes:', error);
    }
  };

  if (!state.currentProject) {
    return (
      <div className="flex-1 flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <h2 className="text-2xl font-semibold text-gray-900 mb-2">
            Vyberte projekt pro úpravu
          </h2>
          <p className="text-gray-600">
            Klikněte na projekt v levém panelu nebo vytvořte nový.
          </p>
        </div>
      </div>
    );
  }

  if (state.currentProject.status === 'processing') {
    return (
      <div className="flex-1 flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <Loader2 className="w-12 h-12 animate-spin text-blue-600 mx-auto mb-4" />
          <h2 className="text-2xl font-semibold text-gray-900 mb-2">
            Zpracovává se...
          </h2>
          <p className="text-gray-600">
            Video se zpracovává pomocí AI. Počkejte prosím.
          </p>
        </div>
      </div>
    );
  }

  if (state.currentProject.status === 'error') {
    return (
      <div className="flex-1 flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <AlertCircle className="w-12 h-12 text-red-600 mx-auto mb-4" />
          <h2 className="text-2xl font-semibold text-gray-900 mb-2">
            Chyba při zpracování
          </h2>
          <p className="text-gray-600 mb-4">
            {state.currentProject.error_message || 'Neočekávaná chyba'}
          </p>
          <button
            onClick={() => window.location.reload()}
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
          >
            Zkusit znovu
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="h-full flex flex-col">
      {/* Video Player */}
      <div className="bg-black flex-shrink-0">
        <ReactPlayer
          ref={playerRef}
          url={state.currentProject.youtube_url}
          width="100%"
          height="300px"
          controls={true}
          playing={isPlaying}
          onPlay={() => setIsPlaying(true)}
          onPause={() => setIsPlaying(false)}
          onProgress={({ playedSeconds }) => setCurrentTime(playedSeconds)}
          config={{
            youtube: {
              playerVars: {
                modestbranding: 1,
                rel: 0,
              },
            },
          }}
        />
      </div>

      {/* Segments Header */}
      <div className="border-b border-gray-200 bg-white px-6 py-4 flex-shrink-0">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-lg font-semibold text-gray-900">
              Titulky ({state.currentProject.segments.length} segmentů)
            </h3>
            <p className="text-sm text-gray-600">
              {state.currentProject.video_title}
            </p>
          </div>
          <div className="flex items-center space-x-4 text-sm">
            <div className="flex items-center space-x-2">
              <span className="text-orange-600 font-medium">
                {state.currentProject.segments?.filter(s => s.status === 'NEEDS_REVIEW').length || 0}
              </span>
              <span className="text-gray-600">k revizi</span>
            </div>

            {/* Tlačítka pro správu zdrojů */}
            <div className="flex items-center space-x-2">
              <button
                className="btn btn-sm btn-secondary"
                onClick={() => setShowSourceManager(!showSourceManager)}
                title="Správa zdrojů titulků"
              >
                <Layers className="w-4 h-4" />
                Zdroje
              </button>

              {currentSourceType && (
                <span className="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded">
                  {currentSourceType === 'youtube_subtitles' ? 'YouTube' : 'Whisper'}
                </span>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Segments List - Fixed height with scrollbar */}
      <div className="flex-1 min-h-0 overflow-y-auto bg-white">
        <div className="divide-y divide-gray-100">
          {state.currentProject?.segments.map((segment) => (
            <SubtitleSegmentRow
              key={segment.segment_id}
              segment={segment}
              isActive={activeSegmentId === segment.segment_id}
              onSegmentClick={handleSegmentClick}
              onTextChange={handleTextChange}
              onApplySuggestion={handleApplySuggestion}
              onRejectSuggestion={handleRejectSuggestion}
              onEditingChange={handleEditingChange}
            />
          ))}
        </div>
      </div>

      {/* Overlay pro správu zdrojů */}
      {showSourceManager && (
        <div className="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full mx-4 max-h-[80vh] overflow-hidden">
            <div className="p-6">
              <div className="flex items-center justify-between mb-4">
                <h2 className="text-xl font-semibold">Správa zdrojů titulků</h2>
                <button
                  className="btn btn-ghost"
                  onClick={() => setShowSourceManager(false)}
                >
                  <X className="w-5 h-5" />
                </button>
              </div>

              <SubtitleSourceManager
                projectId={state.currentProject.project_id}
                onSourceSelect={handleSourceSelect}
                onSourceComparison={handleSourceComparison}
                selectedSourceId={currentSourceId}
              />
            </div>
          </div>
        </div>
      )}

      {/* Overlay pro porovnání zdrojů */}
      {showComparison && (
        <div className="absolute inset-0 bg-white z-50">
          <SubtitleComparison
            projectId={state.currentProject.project_id}
            onClose={() => setShowComparison(false)}
            onApplyChanges={handleApplyComparisonChanges}
          />
        </div>
      )}
    </div>
  );
};

export default EditorPanel;