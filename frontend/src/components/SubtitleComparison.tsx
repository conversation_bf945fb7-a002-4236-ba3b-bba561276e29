import React, { useState, useEffect } from 'react';
import { 
  <PERSON><PERSON><PERSON><PERSON>, 
  <PERSON>R<PERSON>, 
  Co<PERSON>, 
  Check, 
  X, 
  GitCompare,
  Clock,
  FileText,
  Mic
} from 'lucide-react';
import { projectsApi } from '../services/api';

interface ComparisonSegment {
  segment_id: string;
  sequence_number: number;
  start_time_ms: number;
  end_time_ms: number;
  original_text: string;
  corrected_text: string;
  confidence_score?: number;
  status: string;
}

interface ComparisonSource {
  source_id: string;
  source_type: string;
  source_name: string;
  language: string;
  total_segments: number;
  status: string;
  segments: ComparisonSegment[];
}

interface SubtitleComparisonProps {
  projectId: string;
  onClose: () => void;
  onApplyChanges: (sourceId: string, segments: ComparisonSegment[]) => void;
}

const SubtitleComparison: React.FC<SubtitleComparisonProps> = ({
  projectId,
  onClose,
  onApplyChanges
}) => {
  const [comparisonData, setComparisonData] = useState<{
    sources: ComparisonSource[];
  } | null>(null);
  const [loading, setLoading] = useState(true);
  const [selectedSegmentIndex, setSelectedSegmentIndex] = useState(0);
  const [selectedChanges, setSelectedChanges] = useState<{
    [segmentIndex: number]: string; // sourceId
  }>({});

  useEffect(() => {
    loadComparisonData();
  }, [projectId]);

  const loadComparisonData = async () => {
    try {
      setLoading(true);
      const data = await projectsApi.getSourcesComparison(projectId);
      setComparisonData(data);
    } catch (error) {
      console.error('Error loading comparison data:', error);
    } finally {
      setLoading(false);
    }
  };

  const formatTime = (timeMs: number): string => {
    const totalSeconds = Math.floor(timeMs / 1000);
    const minutes = Math.floor(totalSeconds / 60);
    const seconds = totalSeconds % 60;
    const milliseconds = timeMs % 1000;
    return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}.${Math.floor(milliseconds / 100)}`;
  };

  const getSourceIcon = (sourceType: string) => {
    switch (sourceType) {
      case 'youtube_subtitles':
        return <FileText className="w-4 h-4" />;
      case 'whisper_transcription':
        return <Mic className="w-4 h-4" />;
      default:
        return <FileText className="w-4 h-4" />;
    }
  };

  const selectSegmentText = (segmentIndex: number, sourceId: string) => {
    setSelectedChanges(prev => ({
      ...prev,
      [segmentIndex]: sourceId
    }));
  };

  const applySelectedChanges = () => {
    if (!comparisonData) return;

    // Vytvoř nové segmenty na základě vybraných změn
    const primarySource = comparisonData.sources[0];
    const updatedSegments = primarySource.segments.map((segment, index) => {
      const selectedSourceId = selectedChanges[index];
      if (selectedSourceId) {
        const selectedSource = comparisonData.sources.find(s => s.source_id === selectedSourceId);
        const selectedSegment = selectedSource?.segments[index];
        if (selectedSegment) {
          return {
            ...segment,
            corrected_text: selectedSegment.original_text
          };
        }
      }
      return segment;
    });

    onApplyChanges(primarySource.source_id, updatedSegments);
  };

  const navigateSegment = (direction: 'prev' | 'next') => {
    if (!comparisonData) return;
    
    const maxIndex = comparisonData.sources[0]?.segments.length - 1 || 0;
    
    if (direction === 'prev' && selectedSegmentIndex > 0) {
      setSelectedSegmentIndex(selectedSegmentIndex - 1);
    } else if (direction === 'next' && selectedSegmentIndex < maxIndex) {
      setSelectedSegmentIndex(selectedSegmentIndex + 1);
    }
  };

  if (loading) {
    return (
      <div className="subtitle-comparison loading">
        <div className="loading-content">
          <GitCompare className="w-8 h-8 animate-pulse" />
          <p>Načítání porovnání...</p>
        </div>
      </div>
    );
  }

  if (!comparisonData || comparisonData.sources.length < 2) {
    return (
      <div className="subtitle-comparison error">
        <div className="error-content">
          <GitCompare className="w-8 h-8 text-gray-400" />
          <p>Nedostatek zdrojů pro porovnání</p>
          <p className="text-sm text-gray-500">
            Pro porovnání potřebujete alespoň 2 dokončené zdroje titulků
          </p>
          <button className="btn btn-secondary" onClick={onClose}>
            Zpět
          </button>
        </div>
      </div>
    );
  }

  const currentSegments = comparisonData.sources.map(source => 
    source.segments[selectedSegmentIndex]
  ).filter(Boolean);

  const hasChanges = Object.keys(selectedChanges).length > 0;

  return (
    <div className="subtitle-comparison">
      <div className="comparison-header">
        <div className="header-left">
          <button className="btn btn-ghost" onClick={onClose}>
            <ArrowLeft className="w-4 h-4" />
            Zpět
          </button>
          <h2>Porovnání zdrojů titulků</h2>
        </div>
        
        <div className="header-right">
          <div className="segment-navigation">
            <button 
              className="btn btn-sm btn-ghost"
              onClick={() => navigateSegment('prev')}
              disabled={selectedSegmentIndex === 0}
            >
              <ArrowLeft className="w-4 h-4" />
            </button>
            
            <span className="segment-counter">
              {selectedSegmentIndex + 1} / {comparisonData.sources[0]?.segments.length || 0}
            </span>
            
            <button 
              className="btn btn-sm btn-ghost"
              onClick={() => navigateSegment('next')}
              disabled={selectedSegmentIndex >= (comparisonData.sources[0]?.segments.length - 1)}
            >
              <ArrowRight className="w-4 h-4" />
            </button>
          </div>

          {hasChanges && (
            <button 
              className="btn btn-primary"
              onClick={applySelectedChanges}
            >
              <Check className="w-4 h-4" />
              Aplikovat změny ({Object.keys(selectedChanges).length})
            </button>
          )}
        </div>
      </div>

      <div className="comparison-content">
        {currentSegments.length > 0 && (
          <div className="segment-timing">
            <Clock className="w-4 h-4" />
            <span>
              {formatTime(currentSegments[0].start_time_ms)} → {formatTime(currentSegments[0].end_time_ms)}
            </span>
          </div>
        )}

        <div className="sources-comparison">
          {comparisonData.sources.map((source, sourceIndex) => {
            const segment = source.segments[selectedSegmentIndex];
            if (!segment) return null;

            const isSelected = selectedChanges[selectedSegmentIndex] === source.source_id;
            const isDifferent = sourceIndex > 0 && 
              segment.original_text !== comparisonData.sources[0].segments[selectedSegmentIndex]?.original_text;

            return (
              <div 
                key={source.source_id}
                className={`source-comparison ${isSelected ? 'selected' : ''} ${isDifferent ? 'different' : ''}`}
              >
                <div className="source-header">
                  <div className="source-info">
                    {getSourceIcon(source.source_type)}
                    <span className="source-name">{source.source_name}</span>
                    {segment.confidence_score && (
                      <span className="confidence">
                        {segment.confidence_score}% spolehlivost
                      </span>
                    )}
                  </div>
                  
                  {sourceIndex > 0 && isDifferent && (
                    <button
                      className={`btn btn-sm ${isSelected ? 'btn-primary' : 'btn-secondary'}`}
                      onClick={() => selectSegmentText(selectedSegmentIndex, source.source_id)}
                      title={isSelected ? 'Vybráno' : 'Vybrat tento text'}
                    >
                      {isSelected ? <Check className="w-4 h-4" /> : <Copy className="w-4 h-4" />}
                      {isSelected ? 'Vybráno' : 'Vybrat'}
                    </button>
                  )}
                </div>

                <div className="segment-text">
                  <p>{segment.original_text}</p>
                </div>

                {segment.corrected_text !== segment.original_text && (
                  <div className="corrected-text">
                    <small>AI oprava:</small>
                    <p>{segment.corrected_text}</p>
                  </div>
                )}
              </div>
            );
          })}
        </div>
      </div>

      <div className="comparison-footer">
        <div className="comparison-stats">
          <span>
            Celkem segmentů: {comparisonData.sources[0]?.segments.length || 0}
          </span>
          <span>
            Vybrané změny: {Object.keys(selectedChanges).length}
          </span>
        </div>
      </div>
    </div>
  );
};

export default SubtitleComparison;
