import React, { useState, useEffect } from 'react';
import { useProjectContext } from '../contexts/ProjectContext';
import { useWebSocket, WebSocketMessage } from '../services/websocket';
import { Plus, Loader2, AlertCircle, Trash2, Download, FileText, Mic, Wand2, Languages } from 'lucide-react';
import { projectsApi } from '../services/api';

const ProjectListPanel: React.FC = () => {
  const { state, loadProjects, loadProject, createProject } = useProjectContext();
  const [showNewProjectModal, setShowNewProjectModal] = useState(false);
  const [newProjectUrl, setNewProjectUrl] = useState('');
  const [isCreating, setIsCreating] = useState(false);
  const [progressMessages, setProgressMessages] = useState<Map<string, string>>(new Map());
  const [actionLoading, setActionLoading] = useState<Map<string, string>>(new Map());
  const [availableLanguages, setAvailableLanguages] = useState<Map<string, any[]>>(new Map());
  const [showLanguageModal, setShowLanguageModal] = useState<string | null>(null);

  // WebSocket connection for the currently selected project
  const { isConnected, lastMessage } = useWebSocket(state.currentProject?.project_id || null);

  useEffect(() => {
    // Listen for menu events
    if (window.electronAPI) {
      window.electronAPI.onMenuNewProject(() => {
        setShowNewProjectModal(true);
      });

      return () => {
        window.electronAPI?.removeAllListeners('menu-new-project');
      };
    }
  }, []);

  // Handle WebSocket messages
  useEffect(() => {
    if (lastMessage) {
      if (lastMessage.type === 'progress') {
        setProgressMessages(prev => new Map(prev.set(lastMessage.project_id, lastMessage.message)));
      } else if (lastMessage.type === 'status') {
        // Refresh projects when status changes
        loadProjects();

        // Clear progress message for this project
        setProgressMessages(prev => {
          const newMap = new Map(prev);
          newMap.delete(lastMessage.project_id);
          return newMap;
        });
      }
    }
  }, [lastMessage, loadProjects]);

  // Handlery pro akce
  const setProjectActionLoading = (projectId: string, action: string) => {
    setActionLoading(prev => new Map(prev.set(projectId, action)));
  };

  const clearProjectActionLoading = (projectId: string) => {
    setActionLoading(prev => {
      const newMap = new Map(prev);
      newMap.delete(projectId);
      return newMap;
    });
  };

  const handleDeleteProject = async (projectId: string) => {
    if (!confirm('Opravdu chcete smazat tento projekt?')) return;

    try {
      setProjectActionLoading(projectId, 'deleting');
      await projectsApi.deleteProject(projectId);
      await loadProjects();
    } catch (error) {
      console.error('Failed to delete project:', error);
      alert('Nepodařilo se smazat projekt');
    } finally {
      clearProjectActionLoading(projectId);
    }
  };

  const handleExtractSubtitles = async (projectId: string) => {
    try {
      setProjectActionLoading(projectId, 'loading-languages');
      const languagesData = await projectsApi.getAvailableSubtitles(projectId);

      if (languagesData.available_languages && languagesData.available_languages.length > 0) {
        setAvailableLanguages(prev => new Map(prev.set(projectId, languagesData.available_languages)));
        setShowLanguageModal(projectId);
      } else {
        alert('⚠️ Pro toto video nejsou dostupné žádné titulky');
      }
    } catch (error: any) {
      console.error('Failed to get available languages:', error);
      const errorMessage = error.response?.data?.detail || error.message || 'Nepodařilo se načíst dostupné jazyky';

      if (error.response?.status === 403 || errorMessage.includes('soukromé') || errorMessage.includes('dostupné')) {
        alert('❌ Video není dostupné (soukromé nebo smazané)');
        await loadProjects(); // Refresh to show error status
      } else {
        alert(`❌ ${errorMessage}`);
      }
    } finally {
      clearProjectActionLoading(projectId);
    }
  };

  const extractSubtitlesWithLanguage = async (projectId: string, language: string) => {
    try {
      setProjectActionLoading(projectId, 'extracting-subtitles');
      setShowLanguageModal(null); // Zavři modal okamžitě

      const result = await projectsApi.extractSubtitles(projectId, language);

      // Zobrazí výsledek
      if (result.segments_count > 0) {
        alert(`✅ ${result.message}`);
        await loadProjects();
        if (state.currentProject?.project_id === projectId) {
          await loadProject(projectId);
        }
      } else {
        alert(`⚠️ ${result.message}`);
      }
    } catch (error: any) {
      console.error('Failed to extract subtitles:', error);
      const errorMessage = error.response?.data?.detail || 'Nepodařilo se extrahovat titulky';
      alert(`❌ ${errorMessage}`);
    } finally {
      clearProjectActionLoading(projectId);
    }
  };

  const handleDownloadAudio = async (projectId: string) => {
    try {
      setProjectActionLoading(projectId, 'downloading-audio');
      const result = await projectsApi.downloadAudio(projectId);
      alert(result.message);
      await loadProjects();
    } catch (error) {
      console.error('Failed to download audio:', error);
      alert('Nepodařilo se stáhnout audio');
    } finally {
      clearProjectActionLoading(projectId);
    }
  };

  const handleWhisperTranscribe = async (projectId: string) => {
    try {
      setProjectActionLoading(projectId, 'transcribing');
      const result = await projectsApi.transcribeWithWhisper(projectId);
      alert(result.message);
      await loadProjects();
      if (state.currentProject?.project_id === projectId) {
        await loadProject(projectId);
      }
    } catch (error) {
      console.error('Failed to transcribe with Whisper:', error);
      alert('Nepodařilo se provést Whisper transkripci');
    } finally {
      clearProjectActionLoading(projectId);
    }
  };

  const handleAICorrection = async (projectId: string) => {
    try {
      setProjectActionLoading(projectId, 'correcting');
      const result = await projectsApi.correctWithAI(projectId);
      alert(result.message);
      await loadProjects();
      if (state.currentProject?.project_id === projectId) {
        await loadProject(projectId);
      }
    } catch (error) {
      console.error('Failed to correct with AI:', error);
      alert('Nepodařilo se provést AI korekci');
    } finally {
      clearProjectActionLoading(projectId);
    }
  };

  const handleCreateProject = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!newProjectUrl.trim()) return;

    setIsCreating(true);
    try {
      await createProject(newProjectUrl.trim());
      setNewProjectUrl('');
      setShowNewProjectModal(false);
    } catch (error) {
      console.error('Failed to create project:', error);
    } finally {
      setIsCreating(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'loaded':
        return 'text-blue-600 bg-blue-50';
      case 'processing':
        return 'text-blue-600 bg-blue-50';
      case 'subtitles_extracted':
        return 'text-purple-600 bg-purple-50';
      case 'audio_downloaded':
        return 'text-green-600 bg-green-50';
      case 'whisper_transcribed':
        return 'text-indigo-600 bg-indigo-50';
      case 'ai_corrected':
        return 'text-teal-600 bg-teal-50';
      case 'needs_review':
        return 'text-orange-600 bg-orange-50';
      case 'completed':
        return 'text-green-600 bg-green-50';
      case 'error':
        return 'text-red-600 bg-red-50';
      default:
        return 'text-gray-600 bg-gray-50';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'loaded':
        return 'Načteno';
      case 'processing':
        return 'Zpracovává se...';
      case 'subtitles_extracted':
        return 'Titulky extrahovány';
      case 'audio_downloaded':
        return 'Audio staženo';
      case 'whisper_transcribed':
        return 'Whisper dokončen';
      case 'ai_corrected':
        return 'AI korekce dokončena';
      case 'needs_review':
        return 'Vyžaduje kontrolu';
      case 'completed':
        return 'Dokončeno';
      case 'error':
        return 'Chyba';
      default:
        return 'Neznámý stav';
    }
  };

  const getActionLoadingText = (action: string): string => {
    switch (action) {
      case 'extract-subtitles':
        return 'Načítání titulků...';
      case 'download-audio':
        return 'Stahování audio...';
      case 'whisper-transcribe':
        return 'Whisper transkripce...';
      case 'ai-correction':
        return 'AI korekce...';
      case 'delete':
        return 'Mazání...';
      default:
        return 'Zpracovává se...';
    }
  };

  // Helper funkce pro validaci dostupnosti akcí
  const isActionAvailable = (action: string, project: any): boolean => {
    const status = project.status;

    switch (action) {
      case 'extract-subtitles':
        // Titulky lze načíst vždy (pokud není chyba)
        return status !== 'error' && status !== 'creating';

      case 'download-audio':
        // Audio lze stáhnout vždy (pokud není chyba)
        return status !== 'error' && status !== 'creating';

      case 'whisper-transcribe':
        // Whisper vyžaduje stažené audio
        return status === 'audio_downloaded' ||
               status === 'whisper_transcribed' ||
               status === 'ai_corrected';

      case 'ai-correction':
        // AI korekce vyžaduje existující segmenty (z titulků nebo Whisper)
        return status === 'subtitles_extracted' ||
               status === 'whisper_transcribed' ||
               status === 'ai_corrected';

      case 'delete':
        // Smazat lze vždy
        return true;

      default:
        return false;
    }
  };

  return (
    <div className="h-full flex flex-col">
      <div className="p-4 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <h2 className="text-lg font-semibold text-gray-900">Projekty</h2>
          <button
            onClick={() => setShowNewProjectModal(true)}
            className="p-2 text-blue-600 hover:bg-blue-50 rounded-lg transition-colors"
            title="Nový projekt"
          >
            <Plus className="w-5 h-5" />
          </button>
        </div>
      </div>

      <div className="flex-1 overflow-y-auto">
        {state.loading && state.projects.length === 0 ? (
          <div className="flex items-center justify-center h-32">
            <Loader2 className="w-6 h-6 animate-spin text-gray-400" />
          </div>
        ) : state.error ? (
          <div className="p-4 text-center">
            <AlertCircle className="w-8 h-8 text-red-500 mx-auto mb-2" />
            <p className="text-sm text-gray-600">{state.error}</p>
          </div>
        ) : state.projects.length === 0 ? (
          <div className="p-4 text-center">
            <p className="text-sm text-gray-500 mb-4">Žádné projekty</p>
            <button
              onClick={() => setShowNewProjectModal(true)}
              className="text-sm text-blue-600 hover:text-blue-700"
            >
              Vytvořit první projekt
            </button>
          </div>
        ) : (
          <div className="divide-y divide-gray-100">
            {state.projects.map((project) => (
              <div
                key={project.project_id}
                className={`p-4 cursor-pointer hover:bg-gray-50 transition-colors ${
                  state.currentProject?.project_id === project.project_id
                    ? 'bg-blue-50 border-r-2 border-blue-600'
                    : ''
                }`}
                onClick={() => loadProject(project.project_id)}
              >
                <div className="space-y-2">
                  <h3 className="font-medium text-gray-900 truncate">
                    {project.video_title || 'Načítá se...'}
                  </h3>
                  <div className="flex items-center space-x-2">
                    <span
                      className={`px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(
                        project.status
                      )}`}
                    >
                      {getStatusText(project.status)}
                    </span>
                  </div>
                  <p className="text-xs text-gray-500">
                    {project.created_at ?
                      new Date(project.created_at).toLocaleDateString('cs-CZ') :
                      'Neznámé datum'
                    }
                  </p>
                  {/* Progress message */}
                  {progressMessages.has(project.project_id) && (
                    <div className="flex items-center space-x-2 text-xs text-blue-600">
                      <Loader2 className="w-3 h-3 animate-spin" />
                      <span>{progressMessages.get(project.project_id)}</span>
                    </div>
                  )}

                  {/* Action loading message */}
                  {actionLoading.has(project.project_id) && (
                    <div className="flex items-center space-x-2 text-xs text-orange-600">
                      <Loader2 className="w-3 h-3 animate-spin" />
                      <span>{getActionLoadingText(actionLoading.get(project.project_id)!)}</span>
                    </div>
                  )}

                  {/* Error message */}
                  {project.status === 'error' && project.error_message && (
                    <div className="flex items-center space-x-2 text-xs text-red-600">
                      <AlertCircle className="w-3 h-3" />
                      <span>{project.error_message}</span>
                    </div>
                  )}

                  {/* Action buttons */}
                  <div className="flex items-center space-x-1 mt-2">
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        handleDeleteProject(project.project_id);
                      }}
                      disabled={actionLoading.has(project.project_id)}
                      className="p-1 text-red-600 hover:bg-red-50 rounded disabled:opacity-50"
                      title="Smazat projekt"
                    >
                      <Trash2 className="w-4 h-4" />
                    </button>

                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        handleExtractSubtitles(project.project_id);
                      }}
                      disabled={actionLoading.has(project.project_id) || !isActionAvailable('extract-subtitles', project)}
                      className={`p-1 rounded ${
                        isActionAvailable('extract-subtitles', project)
                          ? 'text-blue-600 hover:bg-blue-50'
                          : 'text-gray-400 cursor-not-allowed'
                      } disabled:opacity-50`}
                      title={isActionAvailable('extract-subtitles', project)
                        ? "Načíst titulky"
                        : "Načítání titulků není dostupné"}
                    >
                      <FileText className="w-4 h-4" />
                    </button>

                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        handleDownloadAudio(project.project_id);
                      }}
                      disabled={actionLoading.has(project.project_id) || !isActionAvailable('download-audio', project)}
                      className={`p-1 rounded ${
                        isActionAvailable('download-audio', project)
                          ? 'text-green-600 hover:bg-green-50'
                          : 'text-gray-400 cursor-not-allowed'
                      } disabled:opacity-50`}
                      title={isActionAvailable('download-audio', project)
                        ? "Stáhnout audio"
                        : "Stahování audio není dostupné"}
                    >
                      <Download className="w-4 h-4" />
                    </button>

                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        handleWhisperTranscribe(project.project_id);
                      }}
                      disabled={actionLoading.has(project.project_id) || !isActionAvailable('whisper-transcribe', project)}
                      className={`p-1 rounded ${
                        isActionAvailable('whisper-transcribe', project)
                          ? 'text-purple-600 hover:bg-purple-50'
                          : 'text-gray-400 cursor-not-allowed'
                      } disabled:opacity-50`}
                      title={isActionAvailable('whisper-transcribe', project)
                        ? "Whisper transkripce"
                        : "Whisper transkripce vyžaduje stažené audio"}
                    >
                      <Mic className="w-4 h-4" />
                    </button>

                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        handleAICorrection(project.project_id);
                      }}
                      disabled={actionLoading.has(project.project_id) || !isActionAvailable('ai-correction', project)}
                      className={`p-1 rounded ${
                        isActionAvailable('ai-correction', project)
                          ? 'text-indigo-600 hover:bg-indigo-50'
                          : 'text-gray-400 cursor-not-allowed'
                      } disabled:opacity-50`}
                      title={isActionAvailable('ai-correction', project)
                        ? "AI korekce"
                        : "AI korekce vyžaduje existující segmenty"}
                    >
                      <Wand2 className="w-4 h-4" />
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* New Project Modal */}
      {showNewProjectModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-96 max-w-full mx-4">
            <h3 className="text-lg font-semibold mb-4">Nový projekt</h3>
            <form onSubmit={handleCreateProject}>
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    YouTube URL
                  </label>
                  <input
                    type="url"
                    value={newProjectUrl}
                    onChange={(e) => setNewProjectUrl(e.target.value)}
                    placeholder="https://www.youtube.com/watch?v=..."
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    required
                    disabled={isCreating}
                  />
                </div>
                <div className="flex justify-end space-x-3">
                  <button
                    type="button"
                    onClick={() => {
                      setShowNewProjectModal(false);
                      setNewProjectUrl('');
                    }}
                    className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200"
                    disabled={isCreating}
                  >
                    Zrušit
                  </button>
                  <button
                    type="submit"
                    className="px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700 disabled:opacity-50"
                    disabled={isCreating || !newProjectUrl.trim()}
                  >
                    {isCreating ? (
                      <>
                        <Loader2 className="w-4 h-4 animate-spin inline mr-2" />
                        Vytváří se...
                      </>
                    ) : (
                      'Vytvořit'
                    )}
                  </button>
                </div>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* Language Selection Modal */}
      {showLanguageModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-96 max-w-full mx-4">
            <h3 className="text-lg font-semibold mb-4">Vyberte jazyk titulků</h3>

            {availableLanguages.get(showLanguageModal)?.length === 0 ? (
              <div className="text-center py-8">
                <div className="text-gray-500 mb-4">Žádné titulky nejsou dostupné</div>
                <button
                  onClick={() => setShowLanguageModal(null)}
                  className="px-4 py-2 bg-gray-600 text-white rounded hover:bg-gray-700"
                >
                  Zavřít
                </button>
              </div>
            ) : (
              <>
                <div className="space-y-2 max-h-60 overflow-y-auto">
                  {availableLanguages.get(showLanguageModal)?.map((lang) => (
                    <button
                      key={lang.code}
                      onClick={() => extractSubtitlesWithLanguage(showLanguageModal!, lang.code)}
                      disabled={actionLoading.has(showLanguageModal!)}
                      className="w-full text-left p-3 border rounded hover:bg-gray-50 flex items-center justify-between disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      <div>
                        <div className="font-medium">{lang.name}</div>
                        <div className="text-sm text-gray-500">
                          {lang.code} • {lang.type}
                          {lang.type === 'manuální' && <span className="ml-1 text-green-600">✓</span>}
                        </div>
                      </div>
                      <Languages className="w-4 h-4 text-gray-400" />
                    </button>
                  ))}
                </div>
                <div className="flex justify-end space-x-3 mt-4">
                  <button
                    onClick={() => setShowLanguageModal(null)}
                    disabled={actionLoading.has(showLanguageModal!)}
                    className="px-4 py-2 text-gray-600 border border-gray-300 rounded hover:bg-gray-50 disabled:opacity-50"
                  >
                    Zrušit
                  </button>
                </div>
              </>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

// Pomocné funkce
const getActionLoadingText = (action: string): string => {
  const texts: Record<string, string> = {
    'deleting': 'Mazání...',
    'loading-languages': 'Načítání jazyků...',
    'extracting-subtitles': 'Extrakce titulků...',
    'downloading-audio': 'Stahování audio...',
    'transcribing': 'Whisper transkripce...',
    'correcting': 'AI korekce...'
  };
  return texts[action] || 'Zpracovává se...';
};

export default ProjectListPanel;