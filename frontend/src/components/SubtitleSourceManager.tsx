import React, { useState, useEffect } from 'react';
import { 
  FileText, 
  Mic, 
  Eye, 
  Trash2, 
  Download, 
  AlertCircle, 
  CheckCircle, 
  Loader2,
  GitCompare,
  Play
} from 'lucide-react';
import { projectsApi } from '../services/api';

interface SubtitleSource {
  source_id: string;
  source_type: 'youtube_subtitles' | 'whisper_transcription';
  source_name: string;
  language: string;
  total_segments: number;
  status: 'processing' | 'completed' | 'error';
  created_at: string;
  updated_at: string;
}

interface SubtitleSourceManagerProps {
  projectId: string;
  onSourceSelect: (sourceId: string, sourceType: string) => void;
  onSourceComparison: (sources: SubtitleSource[]) => void;
  selectedSourceId?: string;
}

const SubtitleSourceManager: React.FC<SubtitleSourceManagerProps> = ({
  projectId,
  onSourceSelect,
  onSourceComparison,
  selectedSourceId
}) => {
  const [sources, setSources] = useState<SubtitleSource[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [creatingSource, setCreatingSource] = useState<string | null>(null);

  useEffect(() => {
    loadSources();
  }, [projectId]);

  const loadSources = async () => {
    try {
      setLoading(true);
      const response = await projectsApi.getProjectSubtitleSources(projectId);
      setSources(response.sources);
      setError(null);
    } catch (err) {
      console.error('Error loading sources:', err);
      setError('Chyba při načítání zdrojů titulků');
    } finally {
      setLoading(false);
    }
  };

  const createYouTubeSource = async (language: string = 'cs') => {
    try {
      setCreatingSource('youtube');
      await projectsApi.createYouTubeSubtitleSource(projectId, language);
      await loadSources();
    } catch (err) {
      console.error('Error creating YouTube source:', err);
      setError('Chyba při vytváření YouTube zdroje');
    } finally {
      setCreatingSource(null);
    }
  };

  const createWhisperSource = async (audioFilePath: string) => {
    try {
      setCreatingSource('whisper');
      await projectsApi.createWhisperSubtitleSource(projectId, audioFilePath);
      await loadSources();
    } catch (err) {
      console.error('Error creating Whisper source:', err);
      setError('Chyba při vytváření Whisper zdroje');
    } finally {
      setCreatingSource(null);
    }
  };

  const deleteSource = async (sourceId: string) => {
    if (!confirm('Opravdu chcete smazat tento zdroj titulků?')) return;

    try {
      await projectsApi.deleteSubtitleSource(projectId, sourceId);
      await loadSources();
    } catch (err) {
      console.error('Error deleting source:', err);
      setError('Chyba při mazání zdroje');
    }
  };

  const handleSourceSelect = (source: SubtitleSource) => {
    onSourceSelect(source.source_id, source.source_type);
  };

  const handleComparison = () => {
    const completedSources = sources.filter(s => s.status === 'completed');
    if (completedSources.length >= 2) {
      onSourceComparison(completedSources);
    }
  };

  const getSourceIcon = (sourceType: string) => {
    switch (sourceType) {
      case 'youtube_subtitles':
        return <FileText className="w-4 h-4" />;
      case 'whisper_transcription':
        return <Mic className="w-4 h-4" />;
      default:
        return <FileText className="w-4 h-4" />;
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'processing':
        return <Loader2 className="w-4 h-4 text-blue-500 animate-spin" />;
      case 'error':
        return <AlertCircle className="w-4 h-4 text-red-500" />;
      default:
        return <AlertCircle className="w-4 h-4 text-gray-500" />;
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'completed':
        return 'Dokončeno';
      case 'processing':
        return 'Zpracovává se...';
      case 'error':
        return 'Chyba';
      default:
        return 'Neznámý';
    }
  };

  if (loading && sources.length === 0) {
    return (
      <div className="subtitle-source-manager loading">
        <div className="loading-content">
          <Loader2 className="w-6 h-6 animate-spin" />
          <span>Načítání zdrojů titulků...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="subtitle-source-manager">
      <div className="source-manager-header">
        <h3>Zdroje titulků</h3>
        <div className="source-actions">
          <button
            className="btn btn-secondary btn-sm"
            onClick={() => createYouTubeSource('cs')}
            disabled={creatingSource === 'youtube'}
            title="Načíst titulky z YouTube"
          >
            {creatingSource === 'youtube' ? (
              <Loader2 className="w-4 h-4 animate-spin" />
            ) : (
              <FileText className="w-4 h-4" />
            )}
            YouTube
          </button>
          
          <button
            className="btn btn-secondary btn-sm"
            onClick={() => createWhisperSource(`${projectId}_audio.mp3`)}
            disabled={creatingSource === 'whisper'}
            title="Vytvořit transkripci pomocí Whisper AI"
          >
            {creatingSource === 'whisper' ? (
              <Loader2 className="w-4 h-4 animate-spin" />
            ) : (
              <Mic className="w-4 h-4" />
            )}
            Whisper
          </button>

          {sources.filter(s => s.status === 'completed').length >= 2 && (
            <button
              className="btn btn-primary btn-sm"
              onClick={handleComparison}
              title="Porovnat zdroje titulků"
            >
              <GitCompare className="w-4 h-4" />
              Porovnat
            </button>
          )}
        </div>
      </div>

      {error && (
        <div className="error-message">
          <AlertCircle className="w-4 h-4" />
          <span>{error}</span>
        </div>
      )}

      <div className="sources-list">
        {sources.length === 0 ? (
          <div className="no-sources">
            <FileText className="w-8 h-8 text-gray-400" />
            <p>Žádné zdroje titulků</p>
            <p className="text-sm text-gray-500">
              Začněte načtením titulků z YouTube nebo vytvořením Whisper transkripce
            </p>
          </div>
        ) : (
          sources.map((source) => (
            <div
              key={source.source_id}
              className={`source-item ${selectedSourceId === source.source_id ? 'selected' : ''} ${source.status}`}
            >
              <div className="source-info">
                <div className="source-header">
                  <div className="source-icon">
                    {getSourceIcon(source.source_type)}
                  </div>
                  <div className="source-details">
                    <h4>{source.source_name}</h4>
                    <div className="source-meta">
                      <span className="language">{source.language}</span>
                      <span className="segments">{source.total_segments} segmentů</span>
                      <span className="created">
                        {new Date(source.created_at).toLocaleDateString('cs-CZ')}
                      </span>
                    </div>
                  </div>
                </div>
                
                <div className="source-status">
                  {getStatusIcon(source.status)}
                  <span>{getStatusText(source.status)}</span>
                </div>
              </div>

              <div className="source-actions">
                {source.status === 'completed' && (
                  <>
                    <button
                      className="btn btn-sm btn-ghost"
                      onClick={() => handleSourceSelect(source)}
                      title="Zobrazit v editoru"
                    >
                      <Play className="w-4 h-4" />
                    </button>
                    
                    <button
                      className="btn btn-sm btn-ghost"
                      onClick={() => {/* TODO: Implement preview */}}
                      title="Náhled"
                    >
                      <Eye className="w-4 h-4" />
                    </button>
                  </>
                )}
                
                <button
                  className="btn btn-sm btn-ghost text-red-500"
                  onClick={() => deleteSource(source.source_id)}
                  title="Smazat zdroj"
                >
                  <Trash2 className="w-4 h-4" />
                </button>
              </div>
            </div>
          ))
        )}
      </div>
    </div>
  );
};

export default SubtitleSourceManager;
