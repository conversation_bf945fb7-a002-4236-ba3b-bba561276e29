@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
    font-feature-settings: 'rlig' 1, 'calt' 1;
  }
}

@layer components {
  .btn {
    @apply inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none ring-offset-background;
  }

  .btn-primary {
    @apply bg-blue-600 text-white hover:bg-blue-700;
  }

  .btn-secondary {
    @apply bg-gray-200 text-gray-900 hover:bg-gray-300;
  }

  .btn-destructive {
    @apply bg-red-600 text-white hover:bg-red-700;
  }

  .input {
    @apply flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50;
  }

  .card {
    @apply rounded-lg border bg-card text-card-foreground shadow-sm;
  }

  .loading-spinner {
    @apply animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600;
  }
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  @apply bg-gray-100;
}

::-webkit-scrollbar-thumb {
  @apply bg-gray-400 rounded-full;
}

::-webkit-scrollbar-thumb:hover {
  @apply bg-gray-500;
}

/* Subtitle Source Manager Styles */
.subtitle-source-manager {
  @apply bg-white rounded-lg;
}

.source-manager-header {
  @apply flex items-center justify-between mb-4 pb-4 border-b border-gray-200;
}

.source-manager-header h3 {
  @apply text-lg font-semibold text-gray-900;
}

.source-actions {
  @apply flex items-center space-x-2;
}

.sources-list {
  @apply space-y-3 max-h-96 overflow-y-auto;
}

.source-item {
  @apply border border-gray-200 rounded-lg p-4 hover:border-gray-300 transition-colors;
}

.source-item.selected {
  @apply border-blue-500 bg-blue-50;
}

.source-item.processing {
  @apply border-yellow-300 bg-yellow-50;
}

.source-item.error {
  @apply border-red-300 bg-red-50;
}

.source-info {
  @apply flex items-center justify-between mb-3;
}

.source-header {
  @apply flex items-center space-x-3;
}

.source-icon {
  @apply text-gray-500;
}

.source-details h4 {
  @apply font-medium text-gray-900;
}

.source-meta {
  @apply flex items-center space-x-3 text-sm text-gray-500;
}

.source-status {
  @apply flex items-center space-x-2 text-sm;
}

.no-sources {
  @apply text-center py-8 text-gray-500;
}

.no-sources p {
  @apply mt-2;
}

/* Subtitle Comparison Styles */
.subtitle-comparison {
  @apply h-full flex flex-col bg-white;
}

.subtitle-comparison.loading,
.subtitle-comparison.error {
  @apply items-center justify-center;
}

.loading-content,
.error-content {
  @apply text-center;
}

.comparison-header {
  @apply flex items-center justify-between p-6 border-b border-gray-200 bg-gray-50;
}

.header-left {
  @apply flex items-center space-x-4;
}

.header-right {
  @apply flex items-center space-x-4;
}

.segment-navigation {
  @apply flex items-center space-x-2;
}

.segment-counter {
  @apply px-3 py-1 bg-white rounded text-sm font-medium;
}

.comparison-content {
  @apply flex-1 p-6 overflow-y-auto;
}

.segment-timing {
  @apply flex items-center space-x-2 mb-4 text-sm text-gray-600;
}

.sources-comparison {
  @apply space-y-4;
}

.source-comparison {
  @apply border border-gray-200 rounded-lg p-4;
}

.source-comparison.selected {
  @apply border-blue-500 bg-blue-50;
}

.source-comparison.different {
  @apply border-orange-300 bg-orange-50;
}

.source-header {
  @apply flex items-center justify-between mb-3;
}

.source-info {
  @apply flex items-center space-x-3;
}

.source-name {
  @apply font-medium text-gray-900;
}

.confidence {
  @apply text-xs bg-gray-100 px-2 py-1 rounded;
}

.segment-text {
  @apply mb-2;
}

.segment-text p {
  @apply text-gray-900 leading-relaxed;
}

.corrected-text {
  @apply mt-2 pt-2 border-t border-gray-200;
}

.corrected-text small {
  @apply text-xs text-gray-500 font-medium;
}

.corrected-text p {
  @apply text-gray-700 italic;
}

.comparison-footer {
  @apply p-6 border-t border-gray-200 bg-gray-50;
}

.comparison-stats {
  @apply flex items-center space-x-6 text-sm text-gray-600;
}

/* Error Message Styles */
.error-message {
  @apply flex items-center space-x-2 p-3 bg-red-50 border border-red-200 rounded-lg text-red-700 text-sm mb-4;
}

/* Button size variants */
.btn-sm {
  @apply h-8 px-3 text-xs;
}

.btn-ghost {
  @apply hover:bg-gray-100 hover:text-gray-900;
}