import React, { createContext, useContext, useReducer, useEffect, useCallback } from 'react';
import { Project, ProjectSummary } from '../types';
import { projectsApi } from '../services/api';

interface ProjectState {
  projects: ProjectSummary[];
  currentProject: Project | null;
  loading: boolean;
  error: string | null;
}

type ProjectAction =
  | { type: 'SET_LOADING'; payload: boolean }
  | { type: 'SET_ERROR'; payload: string | null }
  | { type: 'SET_PROJECTS'; payload: ProjectSummary[] }
  | { type: 'SET_CURRENT_PROJECT'; payload: Project | null }
  | { type: 'UPDATE_PROJECT'; payload: Project }
  | { type: 'ADD_PROJECT'; payload: ProjectSummary }
  | { type: 'REMOVE_PROJECT'; payload: string };

const initialState: ProjectState = {
  projects: [],
  currentProject: null,
  loading: false,
  error: null,
};

function projectReducer(state: ProjectState, action: ProjectAction): ProjectState {
  switch (action.type) {
    case 'SET_LOADING':
      return { ...state, loading: action.payload };
    case 'SET_ERROR':
      return { ...state, error: action.payload };
    case 'SET_PROJECTS':
      return { ...state, projects: action.payload };
    case 'SET_CURRENT_PROJECT':
      return { ...state, currentProject: action.payload };
    case 'UPDATE_PROJECT':
      return {
        ...state,
        currentProject: action.payload,
        projects: state.projects.map(p =>
          p.project_id === action.payload.project_id
            ? { ...p, ...action.payload }
            : p
        ),
      };
    case 'ADD_PROJECT':
      return { ...state, projects: [action.payload, ...state.projects] };
    case 'REMOVE_PROJECT':
      return {
        ...state,
        projects: state.projects.filter(p => p.project_id !== action.payload),
        currentProject:
          state.currentProject?.project_id === action.payload
            ? null
            : state.currentProject,
      };
    default:
      return state;
  }
}

interface ProjectContextType {
  state: ProjectState;
  loadProjects: () => Promise<void>;
  loadProject: (projectId: string) => Promise<void>;
  createProject: (youtubeUrl: string) => Promise<void>;
  updateSegment: (segmentId: string, correctedText: string) => Promise<void>;
  deleteProject: (projectId: string) => Promise<void>;
  refreshCurrentProject: () => Promise<void>;
}

const ProjectContext = createContext<ProjectContextType | undefined>(undefined);

export const ProjectProvider: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  const [state, dispatch] = useReducer(projectReducer, initialState);

  const loadProjects = useCallback(async () => {
    dispatch({ type: 'SET_LOADING', payload: true });
    try {
      const projects = await projectsApi.getProjects();
      dispatch({ type: 'SET_PROJECTS', payload: projects });
    } catch (error) {
      dispatch({ type: 'SET_ERROR', payload: 'Failed to load projects' });
    } finally {
      dispatch({ type: 'SET_LOADING', payload: false });
    }
  }, []);

  const loadProject = async (projectId: string) => {
    dispatch({ type: 'SET_LOADING', payload: true });
    try {
      const project = await projectsApi.getProject(projectId);
      dispatch({ type: 'SET_CURRENT_PROJECT', payload: project });
    } catch (error) {
      dispatch({ type: 'SET_ERROR', payload: 'Failed to load project' });
    } finally {
      dispatch({ type: 'SET_LOADING', payload: false });
    }
  };

  const createProject = async (youtubeUrl: string) => {
    dispatch({ type: 'SET_LOADING', payload: true });
    try {
      const project = await projectsApi.createProject(youtubeUrl);
      dispatch({ type: 'ADD_PROJECT', payload: project });
      dispatch({ type: 'SET_CURRENT_PROJECT', payload: project });
    } catch (error) {
      dispatch({ type: 'SET_ERROR', payload: 'Failed to create project' });
      throw error;
    } finally {
      dispatch({ type: 'SET_LOADING', payload: false });
    }
  };

  const updateSegment = async (segmentId: string, correctedText: string) => {
    if (!state.currentProject) return;

    try {
      const updatedSegment = await projectsApi.updateSegment(
        state.currentProject.project_id,
        segmentId,
        correctedText
      );

      const updatedProject = {
        ...state.currentProject,
        segments: state.currentProject.segments.map(segment =>
          segment.segment_id === segmentId ? updatedSegment : segment
        ),
      };

      dispatch({ type: 'UPDATE_PROJECT', payload: updatedProject });
    } catch (error) {
      dispatch({ type: 'SET_ERROR', payload: 'Failed to update segment' });
      throw error;
    }
  };

  const deleteProject = async (projectId: string) => {
    try {
      await projectsApi.deleteProject(projectId);
      dispatch({ type: 'REMOVE_PROJECT', payload: projectId });
    } catch (error) {
      dispatch({ type: 'SET_ERROR', payload: 'Failed to delete project' });
      throw error;
    }
  };

  const refreshCurrentProject = async () => {
    if (!state.currentProject) return;
    await loadProject(state.currentProject.project_id);
  };

  // Auto-refresh projects every 10 seconds
  useEffect(() => {
    loadProjects();
    const interval = setInterval(loadProjects, 10000);
    return () => clearInterval(interval);
  }, [loadProjects]);

  return (
    <ProjectContext.Provider
      value={{
        state,
        loadProjects,
        loadProject,
        createProject,
        updateSegment,
        deleteProject,
        refreshCurrentProject,
      }}
    >
      {children}
    </ProjectContext.Provider>
  );
};

export const useProjectContext = () => {
  const context = useContext(ProjectContext);
  if (!context) {
    throw new Error('useProjectContext must be used within a ProjectProvider');
  }
  return context;
};