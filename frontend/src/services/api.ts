import axios from 'axios';
import {
  Project,
  ProjectSummary,
  SubtitleSegment,
  UserDictionaryTerm,
  CreateProjectRequest,
  UpdateSegmentRequest,
  CreateDictionaryTermRequest,
} from '../types';

const BACKEND_PORT = process.env.BACKEND_PORT || '8001';
const BACKEND_HOST = process.env.BACKEND_HOST || 'localhost';
const API_BASE_URL = `http://${BACKEND_HOST}:${BACKEND_PORT}/api/v1`;

const apiClient = axios.create({
  baseURL: API_BASE_URL,
  timeout: 60000, // Zvýšeno na 60 sekund pro základní operace
  headers: {
    'Content-Type': 'application/json',
  },
});

// Speciální klient pro dlouhé operace (stahování, transkripce, AI korekce)
const longOperationClient = axios.create({
  baseURL: API_BASE_URL,
  timeout: 300000, // 5 minut pro dlouhé operace
  headers: {
    'Content-Type': 'application/json',
  },
});

// Společné interceptory pro oba klienty
const setupInterceptors = (client: typeof apiClient) => {
  // Request interceptor for error handling
  client.interceptors.request.use(
    (config) => config,
    (error) => Promise.reject(error)
  );

  // Response interceptor for error handling
  client.interceptors.response.use(
    (response) => response,
    (error) => {
      console.error('API Error:', error.response?.data || error.message);
      return Promise.reject(error);
    }
  );
};

setupInterceptors(apiClient);
setupInterceptors(longOperationClient);

export const projectsApi = {
  async createProject(youtubeUrl: string): Promise<Project> {
    const response = await apiClient.post<Project>('/projects', {
      youtube_url: youtubeUrl,
    });
    return response.data;
  },

  async getProjects(): Promise<ProjectSummary[]> {
    const response = await apiClient.get<ProjectSummary[]>('/projects');
    return response.data;
  },

  async getProject(projectId: string): Promise<Project> {
    const response = await apiClient.get<Project>(`/projects/${projectId}`);
    return response.data;
  },

  async updateSegment(
    projectId: string,
    segmentId: string,
    correctedText: string
  ): Promise<SubtitleSegment> {
    const response = await apiClient.put<SubtitleSegment>(
      `/projects/${projectId}/segments/${segmentId}`,
      { corrected_text: correctedText } as UpdateSegmentRequest
    );
    return response.data;
  },

  async applySuggestion(
    projectId: string,
    segmentId: string,
    suggestionId: string
  ): Promise<void> {
    await apiClient.post(
      `/projects/${projectId}/segments/${segmentId}/suggestions/${suggestionId}/apply`
    );
  },

  async rejectSuggestion(
    projectId: string,
    segmentId: string,
    suggestionId: string
  ): Promise<void> {
    await apiClient.post(
      `/projects/${projectId}/segments/${segmentId}/suggestions/${suggestionId}/reject`
    );
  },

  async getExportPreview(projectId: string): Promise<any> {
    const response = await apiClient.get(`/projects/${projectId}/export/preview`);
    return response.data;
  },

  async exportProject(projectId: string): Promise<Blob> {
    const response = await apiClient.post(
      `/projects/${projectId}/export`,
      {},
      { responseType: 'blob' }
    );
    return response.data;
  },

  // Nové API metody pro manuální akce
  async getAvailableSubtitles(projectId: string): Promise<any> {
    const response = await apiClient.get(`/projects/${projectId}/available-subtitles`);
    return response.data;
  },

  async extractSubtitles(projectId: string, language: string = 'cs'): Promise<any> {
    const response = await apiClient.post(`/projects/${projectId}/extract-subtitles?language=${language}`);
    return response.data;
  },

  async downloadAudio(projectId: string): Promise<any> {
    const response = await longOperationClient.post(`/projects/${projectId}/download-audio`);
    return response.data;
  },

  async transcribeWithWhisper(projectId: string): Promise<any> {
    const response = await longOperationClient.post(`/projects/${projectId}/transcribe-whisper`);
    return response.data;
  },

  async correctWithAI(projectId: string): Promise<any> {
    const response = await longOperationClient.post(`/projects/${projectId}/correct-ai`);
    return response.data;
  },

  async deleteProject(projectId: string): Promise<void> {
    await apiClient.delete(`/projects/${projectId}`);
  },
};

export const dictionaryApi = {
  async getTerms(): Promise<UserDictionaryTerm[]> {
    const response = await apiClient.get<UserDictionaryTerm[]>('/dictionary');
    return response.data;
  },

  async addTerm(phrase: string, caseSensitive: boolean): Promise<UserDictionaryTerm> {
    const response = await apiClient.post<UserDictionaryTerm>('/dictionary', {
      phrase,
      case_sensitive: caseSensitive,
    } as CreateDictionaryTermRequest);
    return response.data;
  },

  async deleteTerm(termId: string): Promise<void> {
    await apiClient.delete(`/dictionary/${termId}`);
  },
};

export default apiClient;