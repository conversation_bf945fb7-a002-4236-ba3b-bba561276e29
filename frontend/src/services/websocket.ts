/**
 * WebSocket service for real-time project updates
 */

import React from 'react';

export interface ProgressUpdate {
  type: 'progress';
  project_id: string;
  message: string;
  progress?: number;
  timestamp: string;
}

export interface StatusUpdate {
  type: 'status';
  project_id: string;
  status: string;
  error_message?: string;
  timestamp: string;
}

export type WebSocketMessage = ProgressUpdate | StatusUpdate;

export class WebSocketService {
  private ws: WebSocket | null = null;
  private projectId: string | null = null;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectDelay = 1000; // Start with 1 second
  private listeners: Map<string, Set<(message: WebSocketMessage) => void>> = new Map();

  constructor(private baseUrl: string = `ws://${process.env.BACKEND_HOST || 'localhost'}:${process.env.BACKEND_PORT || '8001'}/api/v1`) {}

  /**
   * Connect to WebSocket for a specific project
   */
  connect(projectId: string): Promise<void> {
    return new Promise((resolve, reject) => {
      if (this.ws && this.ws.readyState === WebSocket.OPEN && this.projectId === projectId) {
        resolve();
        return;
      }

      this.disconnect();
      this.projectId = projectId;

      const wsUrl = `${this.baseUrl}/ws/projects/${projectId}`;
      console.log(`Connecting to WebSocket: ${wsUrl}`);

      this.ws = new WebSocket(wsUrl);

      this.ws.onopen = () => {
        console.log(`WebSocket connected for project ${projectId}`);
        this.reconnectAttempts = 0;
        this.reconnectDelay = 1000;
        resolve();
      };

      this.ws.onmessage = (event) => {
        try {
          const message: WebSocketMessage = JSON.parse(event.data);
          this.handleMessage(message);
        } catch (error) {
          console.error('Error parsing WebSocket message:', error);
        }
      };

      this.ws.onclose = (event) => {
        console.log(`WebSocket closed for project ${projectId}:`, event.code, event.reason);
        
        // Auto-reconnect if not manually closed
        if (event.code !== 1000 && this.reconnectAttempts < this.maxReconnectAttempts) {
          this.scheduleReconnect();
        }
      };

      this.ws.onerror = (error) => {
        console.error('WebSocket error:', error);
        reject(error);
      };

      // Timeout for connection
      setTimeout(() => {
        if (this.ws?.readyState !== WebSocket.OPEN) {
          reject(new Error('WebSocket connection timeout'));
        }
      }, 5000);
    });
  }

  /**
   * Disconnect from WebSocket
   */
  disconnect(): void {
    if (this.ws) {
      this.ws.close(1000, 'Manual disconnect');
      this.ws = null;
    }
    this.projectId = null;
    this.reconnectAttempts = 0;
  }

  /**
   * Add listener for WebSocket messages
   */
  addListener(type: string, callback: (message: WebSocketMessage) => void): void {
    if (!this.listeners.has(type)) {
      this.listeners.set(type, new Set());
    }
    this.listeners.get(type)!.add(callback);
  }

  /**
   * Remove listener for WebSocket messages
   */
  removeListener(type: string, callback: (message: WebSocketMessage) => void): void {
    const typeListeners = this.listeners.get(type);
    if (typeListeners) {
      typeListeners.delete(callback);
      if (typeListeners.size === 0) {
        this.listeners.delete(type);
      }
    }
  }

  /**
   * Send ping to keep connection alive
   */
  ping(): void {
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      this.ws.send(JSON.stringify({ type: 'ping' }));
    }
  }

  /**
   * Get connection status
   */
  isConnected(): boolean {
    return this.ws?.readyState === WebSocket.OPEN;
  }

  private handleMessage(message: WebSocketMessage): void {
    console.log('WebSocket message received:', message);

    // Notify type-specific listeners
    const typeListeners = this.listeners.get(message.type);
    if (typeListeners) {
      typeListeners.forEach(callback => callback(message));
    }

    // Notify all listeners
    const allListeners = this.listeners.get('*');
    if (allListeners) {
      allListeners.forEach(callback => callback(message));
    }
  }

  private scheduleReconnect(): void {
    this.reconnectAttempts++;
    console.log(`Scheduling WebSocket reconnect attempt ${this.reconnectAttempts}/${this.maxReconnectAttempts} in ${this.reconnectDelay}ms`);

    setTimeout(() => {
      if (this.projectId) {
        this.connect(this.projectId).catch(error => {
          console.error('WebSocket reconnect failed:', error);
        });
      }
    }, this.reconnectDelay);

    // Exponential backoff
    this.reconnectDelay = Math.min(this.reconnectDelay * 2, 30000); // Max 30 seconds
  }
}

// Global WebSocket service instance
export const webSocketService = new WebSocketService();

// React hook for using WebSocket
export function useWebSocket(projectId: string | null) {
  const [isConnected, setIsConnected] = React.useState(false);
  const [lastMessage, setLastMessage] = React.useState<WebSocketMessage | null>(null);

  React.useEffect(() => {
    if (!projectId) {
      webSocketService.disconnect();
      setIsConnected(false);
      return;
    }

    const handleMessage = (message: WebSocketMessage) => {
      setLastMessage(message);
    };

    const handleConnection = () => {
      setIsConnected(true);
    };

    const handleDisconnection = () => {
      setIsConnected(false);
    };

    // Add listeners
    webSocketService.addListener('*', handleMessage);

    // Connect
    webSocketService.connect(projectId)
      .then(handleConnection)
      .catch(error => {
        console.error('Failed to connect WebSocket:', error);
        setIsConnected(false);
      });

    // Ping interval to keep connection alive
    const pingInterval = setInterval(() => {
      webSocketService.ping();
    }, 30000); // Every 30 seconds

    return () => {
      clearInterval(pingInterval);
      webSocketService.removeListener('*', handleMessage);
      webSocketService.disconnect();
      setIsConnected(false);
    };
  }, [projectId]);

  return { isConnected, lastMessage };
}
