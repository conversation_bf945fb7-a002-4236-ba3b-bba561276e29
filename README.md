# AI Korektor Titulků

Pokročilá desktopová aplikace pro automatickou korekci titulků z YouTube videí pomocí umělé inteligence.

## 🎯 Přehled

AI Korektor Titulků je nástroj pro asistovanou AI korekturu automaticky generovaných titulků z YouTube. Aplikace využívá OpenAI Whisper pro transkripci a GPT-4o-mini pro sémantickou korekci, čímž poskytuje vysoce kvalitní výsledky bez závislosti na lokálním hardwaru.

### ✨ Klíčové funkce

- 🎥 **YouTube integrace** - Automatické stahování audio z YouTube videí
- 🤖 **AI transkripce** - OpenAI Whisper API pro přesnou transkripci
- ✏️ **Inteligentní korekce** - GPT-4o-mini pro gramatické a sémantické opravy
- 🎛️ **Pokročilý editor** - Real-time editace s video synchronizací
- 💡 **AI návrhy** - Inteligentní návrhy korekce s batch operacemi
- ⌨️ **Klávesové zkratky** - Produktivní workflow s kompletními zkratkami
- 📤 **Export** - Podpora SRT, VTT, TXT, JSON a CSV formátů
- 🔄 **Real-time updates** - WebSocket komunikace pro live progress

## 🚀 Rychlé spuštění

### Předpoklady

- **Node.js** 18+ 
- **Python** 3.12+
- **OpenAI API klíč** (pro Whisper a GPT-4o-mini)

### Instalace a spuštění

1. **Klonování repozitáře**
```bash
git clone <repository-url>
cd AI-korektor-titulku
```

2. **Konfigurace API klíčů**
```bash
# Upravte .env soubor a přidejte váš OpenAI API klíč
OPENAI_API_KEY="sk-your-api-key-here"
```

3. **Spuštění aplikace**
```bash
# Spustí backend i frontend s automatickou kontrolou portů
./start.sh
```

4. **Přístup k aplikaci**
- **Frontend**: http://localhost:3000
- **Backend API**: http://localhost:8001
- **API dokumentace**: http://localhost:8001/docs

5. **Zastavení aplikace**
```bash
./stop.sh
```

## 🎮 Použití

### Základní workflow

1. **Vytvoření projektu** - Vložte YouTube URL
2. **Automatické zpracování** - AI automaticky:
   - Stáhne audio z videa
   - Provede transkripci pomocí Whisper
   - Vygeneruje korekce pomocí GPT-4o-mini
3. **Editace a korekce** - Využijte:
   - Real-time editor s video synchronizací
   - AI návrhy korekce s batch operacemi
   - Klávesové zkratky pro rychlou práci
4. **Export** - Exportujte do požadovaného formátu

### ⌨️ Klávesové zkratky

#### Přehrávání
- `Mezerník` - Přehrát/Pozastavit
- `Ctrl+←` - Přetočit zpět (5s)
- `Ctrl+→` - Přetočit vpřed (5s)

#### Navigace
- `↑` - Předchozí segment
- `↓` - Následující segment
- `1-9` - Přejít na konkrétní segment

#### Editace
- `F2` - Editovat aktivní segment
- `Enter` - Přijmout AI návrhy
- `Esc` - Zamítnout návrhy/zrušit editaci
- `Ctrl+Enter` - Uložit editaci

#### Systém
- `Ctrl+S` - Export projektu
- `Ctrl+F` - Celá obrazovka
- `Ctrl+H` nebo `?` - Nápověda

## 🛠️ Technické detaily

### Architektura

```
┌─────────────────┐    HTTP/REST    ┌─────────────────┐
│  React Frontend │ ←──────────────→ │  FastAPI Server │
│  (Port 3000)    │                 │  (Port 8001)    │
└─────────────────┘                 └─────────────────┘
                                             │
                                             ▼
                                    ┌─────────────────┐
                                    │  SQLite DB      │
                                    │  OpenAI APIs    │
                                    └─────────────────┘
```

### Technologický stack

- **Frontend**: React 18+, TypeScript, Tailwind CSS, Webpack
- **Backend**: FastAPI, Python 3.12+, SQLAlchemy, SQLite
- **AI služby**: OpenAI Whisper API, GPT-4o-mini
- **Video**: yt-dlp pro YouTube, React Player pro přehrávání

### Konfigurace (.env)

```bash
# API klíče
OPENAI_API_KEY="sk-your-api-key-here"
ANTHROPIC_API_KEY=""
PERPLEXITY_API_KEY=""

# Porty služeb
BACKEND_PORT=8001
FRONTEND_PORT=3000
BACKEND_HOST=0.0.0.0
FRONTEND_HOST=localhost

# Databáze
DATABASE_URL=sqlite:///./app.db

# CORS
CORS_ORIGINS="http://localhost:3000,http://127.0.0.1:3000"

# Soubory
MAX_FILE_SIZE=100000000
UPLOAD_DIR=./uploads

# Logging
LOG_LEVEL=INFO
```

## 📁 Struktura projektu

```
AI-korektor-titulku/
├── backend/                 # FastAPI backend
│   ├── app/
│   │   ├── api/v1/         # API endpointy
│   │   ├── core/           # Konfigurace a databáze
│   │   ├── models/         # SQLAlchemy modely
│   │   ├── schemas/        # Pydantic schémata
│   │   ├── services/       # Business logika
│   │   └── utils/          # Pomocné funkce
│   └── requirements.txt    # Python závislosti
├── frontend/               # React frontend
│   ├── src/
│   │   ├── components/     # React komponenty
│   │   ├── contexts/       # React Context
│   │   ├── hooks/          # Custom hooks
│   │   ├── services/       # API klient
│   │   └── types/          # TypeScript typy
│   └── package.json        # Node.js závislosti
├── logs/                   # Aplikační logy
├── .env                    # Konfigurace prostředí
├── start.sh               # Spouštěcí skript
├── stop.sh                # Zastavovací skript
├── PRD.md                 # Produktová specifikace
└── tasks.md               # Seznam implementačních úkolů
```

## 🔧 Vývoj

### Spuštění pro vývoj

```bash
# Backend (port 8001)
cd backend
python -m venv venv
source venv/bin/activate  # Linux/Mac
pip install -r requirements.txt
python -m uvicorn app.main:app --reload --host 0.0.0.0 --port 8001

# Frontend (port 3000)
cd frontend
npm install
npm run dev
```

### Testování

```bash
# API testování
curl http://localhost:8001/health
curl http://localhost:8001/api/v1/projects/

# Frontend testování
# Otevřete http://localhost:3000 v prohlížeči
```

## 📚 Dokumentace

- **PRD.md** - Kompletní produktová specifikace
- **tasks.md** - Detailní seznam implementačních úkolů
- **API dokumentace** - http://localhost:8001/docs (automaticky generovaná)

## 🤝 Přispívání

1. Fork repozitáře
2. Vytvořte feature branch (`git checkout -b feature/nova-funkce`)
3. Commitněte změny (`git commit -am 'Přidána nová funkce'`)
4. Push do branch (`git push origin feature/nova-funkce`)
5. Vytvořte Pull Request

## 📄 Licence

Tento projekt je licencován pod MIT licencí - viz LICENSE soubor pro detaily.

## 🆘 Podpora

Pro technické dotazy a podporu:
- Vytvořte issue v GitHub repozitáři
- Kontaktujte vývojový tým

---

**Status**: ✅ **KOMPLETNĚ IMPLEMENTOVÁNO** - Všechny funkce podle PRD jsou hotové a testované
**Verze**: 1.0.0
**Poslední aktualizace**: 16. července 2025
