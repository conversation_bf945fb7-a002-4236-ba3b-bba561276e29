"""
Service pro správu různ<PERSON><PERSON> zdro<PERSON><PERSON> titulků.
Umožňuje ukládat a porovnávat YouTube titulky vs. Whisper transkrip<PERSON>.
"""

from typing import List, Dict, Any, Optional
from sqlalchemy.orm import Session
from app.models import Project, SubtitleSource, SubtitleSegment
from app.services.youtube_service import YouTubeService
from app.services.openai_service import OpenAIService
import uuid
from datetime import datetime
import logging

logger = logging.getLogger(__name__)


class SubtitleSourceService:
    def __init__(self, db: Session):
        self.db = db
        self.youtube_service = YouTubeService()
        self.openai_service = OpenAIService()

    def create_youtube_subtitle_source(self, project_id: str, language: str = "cs") -> SubtitleSource:
        """
        Vytvoří zdroj titulků z YouTube.
        """
        try:
            # Získej projekt
            project = self.db.query(Project).filter(Project.project_id == project_id).first()
            if not project:
                raise ValueError(f"Project {project_id} not found")

            # Vytvoř zdroj
            source = SubtitleSource(
                source_id=str(uuid.uuid4()),
                project_id=project_id,
                source_type="youtube_subtitles",
                source_name=f"YouTube titulky ({language})",
                language=language,
                status="processing"
            )
            self.db.add(source)
            self.db.flush()

            # Extrahuj titulky z YouTube
            subtitles_data = self.youtube_service.extract_subtitles(
                project.youtube_url, 
                language=language
            )

            if not subtitles_data:
                source.status = "error"
                source.error_message = "Žádné titulky nebyly nalezeny"
                self.db.commit()
                return source

            # Vytvoř segmenty
            segments_created = 0
            for i, subtitle_data in enumerate(subtitles_data):
                segment = SubtitleSegment(
                    segment_id=str(uuid.uuid4()),
                    project_id=project_id,
                    source_id=source.source_id,
                    sequence_number=i + 1,
                    start_time_ms=int(subtitle_data.get('start', 0) * 1000),
                    end_time_ms=int(subtitle_data.get('end', 0) * 1000),
                    original_text=subtitle_data.get('text', ''),
                    corrected_text=subtitle_data.get('text', ''),
                    status="youtube_extracted"
                )
                self.db.add(segment)
                segments_created += 1

            # Aktualizuj metadata zdroje
            source.total_segments = segments_created
            source.status = "completed"
            self.db.commit()

            logger.info(f"Created YouTube subtitle source with {segments_created} segments")
            return source

        except Exception as e:
            logger.error(f"Error creating YouTube subtitle source: {str(e)}")
            if 'source' in locals():
                source.status = "error"
                source.error_message = str(e)
                self.db.commit()
            raise

    def create_whisper_subtitle_source(self, project_id: str, audio_file_path: str) -> SubtitleSource:
        """
        Vytvoří zdroj titulků z Whisper transkripce.
        """
        try:
            # Získej projekt
            project = self.db.query(Project).filter(Project.project_id == project_id).first()
            if not project:
                raise ValueError(f"Project {project_id} not found")

            # Vytvoř zdroj
            source = SubtitleSource(
                source_id=str(uuid.uuid4()),
                project_id=project_id,
                source_type="whisper_transcription",
                source_name="Whisper AI transkripce",
                language="cs",
                status="processing"
            )
            self.db.add(source)
            self.db.flush()

            # Proveď Whisper transkripci
            whisper_segments = self.openai_service.transcribe_audio(
                audio_file_path,
                language="cs"
            )

            if not whisper_segments:
                source.status = "error"
                source.error_message = "Whisper transkripce nevrátila žádné segmenty"
                self.db.commit()
                return source

            # Vytvoř segmenty
            segments_created = 0
            for i, whisper_data in enumerate(whisper_segments):
                segment = SubtitleSegment(
                    segment_id=str(uuid.uuid4()),
                    project_id=project_id,
                    source_id=source.source_id,
                    sequence_number=i + 1,
                    start_time_ms=int(whisper_data.get('start', 0) * 1000),
                    end_time_ms=int(whisper_data.get('end', 0) * 1000),
                    original_text=whisper_data.get('text', ''),
                    corrected_text=whisper_data.get('text', ''),
                    confidence_score=int(whisper_data.get('confidence', 0.8) * 100),
                    status="whisper_transcribed"
                )
                self.db.add(segment)
                segments_created += 1

            # Aktualizuj metadata zdroje
            source.total_segments = segments_created
            source.status = "completed"
            self.db.commit()

            logger.info(f"Created Whisper subtitle source with {segments_created} segments")
            return source

        except Exception as e:
            logger.error(f"Error creating Whisper subtitle source: {str(e)}")
            if 'source' in locals():
                source.status = "error"
                source.error_message = str(e)
                self.db.commit()
            raise

    def get_project_sources(self, project_id: str) -> List[SubtitleSource]:
        """
        Získá všechny zdroje titulků pro projekt.
        """
        return self.db.query(SubtitleSource).filter(
            SubtitleSource.project_id == project_id
        ).order_by(SubtitleSource.created_at).all()

    def get_source_segments(self, source_id: str) -> List[SubtitleSegment]:
        """
        Získá všechny segmenty pro daný zdroj.
        """
        return self.db.query(SubtitleSegment).filter(
            SubtitleSegment.source_id == source_id
        ).order_by(SubtitleSegment.sequence_number).all()

    def delete_source(self, source_id: str) -> bool:
        """
        Smaže zdroj a všechny jeho segmenty.
        """
        try:
            source = self.db.query(SubtitleSource).filter(
                SubtitleSource.source_id == source_id
            ).first()
            
            if source:
                self.db.delete(source)
                self.db.commit()
                logger.info(f"Deleted subtitle source {source_id}")
                return True
            return False
        except Exception as e:
            logger.error(f"Error deleting subtitle source {source_id}: {str(e)}")
            self.db.rollback()
            raise

    def get_source_comparison_data(self, project_id: str) -> Dict[str, Any]:
        """
        Připraví data pro porovnání různých zdrojů titulků.
        """
        sources = self.get_project_sources(project_id)
        
        comparison_data = {
            "project_id": project_id,
            "sources": [],
            "comparison_matrix": []
        }
        
        for source in sources:
            segments = self.get_source_segments(source.source_id)
            comparison_data["sources"].append({
                "source_id": source.source_id,
                "source_type": source.source_type,
                "source_name": source.source_name,
                "language": source.language,
                "total_segments": len(segments),
                "status": source.status,
                "created_at": source.created_at.isoformat(),
                "segments": [
                    {
                        "segment_id": seg.segment_id,
                        "sequence_number": seg.sequence_number,
                        "start_time_ms": seg.start_time_ms,
                        "end_time_ms": seg.end_time_ms,
                        "original_text": seg.original_text,
                        "corrected_text": seg.corrected_text,
                        "confidence_score": seg.confidence_score,
                        "status": seg.status
                    }
                    for seg in segments
                ]
            })
        
        return comparison_data
