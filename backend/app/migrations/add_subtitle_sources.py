"""
<PERSON><PERSON><PERSON> pro přidání podpory zdrojů titulků.
Přidává tabulku subtitle_sources a rozšiřuje subtitle_segments.
"""

from sqlalchemy import text
from app.core.database import engine
import logging

logger = logging.getLogger(__name__)


def run_migration():
    """
    <PERSON><PERSON>tí migraci pro přidání podpory zdrojů titulků.
    """
    try:
        with engine.connect() as connection:
            # Začni transakci
            trans = connection.begin()
            
            try:
                # 1. Vytvoř tabulku subtitle_sources
                connection.execute(text("""
                    CREATE TABLE IF NOT EXISTS subtitle_sources (
                        source_id VARCHAR PRIMARY KEY,
                        project_id VARCHAR NOT NULL,
                        source_type VARCHAR NOT NULL,
                        source_name VARCHAR NOT NULL,
                        language VARCHAR,
                        is_automatic BOOLEAN DEFAULT 0,
                        total_segments INTEGER DEFAULT 0,
                        total_duration_ms INTEGER DEFAULT 0,
                        status VARCHAR DEFAULT 'processing',
                        error_message TEXT,
                        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                        file_path VARCHAR,
                        FOREIGN KEY (project_id) REFERENCES projects (project_id)
                    )
                """))
                
                # 2. Zkontroluj, jestli sloupec source_id už existuje v subtitle_segments
                result = connection.execute(text("""
                    PRAGMA table_info(subtitle_segments)
                """))
                
                columns = [row[1] for row in result.fetchall()]
                
                if 'source_id' not in columns:
                    # 3. Přidej sloupec source_id do subtitle_segments
                    connection.execute(text("""
                        ALTER TABLE subtitle_segments 
                        ADD COLUMN source_id VARCHAR
                    """))
                    
                    # 4. Přidej další nové sloupce
                    connection.execute(text("""
                        ALTER TABLE subtitle_segments 
                        ADD COLUMN confidence_score INTEGER
                    """))
                    
                    connection.execute(text("""
                        ALTER TABLE subtitle_segments 
                        ADD COLUMN created_at DATETIME DEFAULT CURRENT_TIMESTAMP
                    """))
                    
                    connection.execute(text("""
                        ALTER TABLE subtitle_segments 
                        ADD COLUMN updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
                    """))
                
                # 5. Pro existující projekty vytvoř default zdroj
                existing_projects = connection.execute(text("""
                    SELECT DISTINCT project_id FROM subtitle_segments 
                    WHERE source_id IS NULL
                """)).fetchall()
                
                for project in existing_projects:
                    project_id = project[0]
                    
                    # Vytvoř default zdroj pro existující segmenty
                    source_id = f"{project_id}_default_source"
                    
                    connection.execute(text("""
                        INSERT OR IGNORE INTO subtitle_sources 
                        (source_id, project_id, source_type, source_name, language, status, total_segments)
                        VALUES (:source_id, :project_id, 'youtube_subtitles', 'Původní titulky', 'cs', 'completed',
                                (SELECT COUNT(*) FROM subtitle_segments WHERE project_id = :project_id))
                    """), {
                        'source_id': source_id,
                        'project_id': project_id
                    })
                    
                    # Aktualizuj existující segmenty
                    connection.execute(text("""
                        UPDATE subtitle_segments 
                        SET source_id = :source_id 
                        WHERE project_id = :project_id AND source_id IS NULL
                    """), {
                        'source_id': source_id,
                        'project_id': project_id
                    })
                
                # Potvrď transakci
                trans.commit()
                logger.info("Migrace subtitle_sources byla úspěšně dokončena")
                
            except Exception as e:
                # Rollback při chybě
                trans.rollback()
                raise e
                
    except Exception as e:
        logger.error(f"Chyba při migraci subtitle_sources: {str(e)}")
        raise


if __name__ == "__main__":
    run_migration()
