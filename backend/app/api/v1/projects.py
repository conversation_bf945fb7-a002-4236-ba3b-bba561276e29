"""Project management endpoints for AI Korektor Titulků."""

from typing import List
from datetime import datetime
from fastapi import APIRouter, Depends, HTTPException, BackgroundTasks, Body
from sqlalchemy.orm import Session

from app.core.database import get_db
from app.schemas import CreateProjectRequest, Project, ProjectSummary, UpdateSegmentRequest, SubtitleSegment
from app.services.project_service import ProjectService
from app.services.youtube_service import YouTubeService
from app.services.advanced_export_service import AdvancedExportService
from app.models import Project as ProjectModel

router = APIRouter(prefix="/projects", tags=["projects"])


@router.post("/", response_model=Project)
async def create_project(
    project_data: CreateProjectRequest,
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_db)
):
    """
    Vytvoří nový projekt a spustí zpracování na pozadí
    """
    # Validace YouTube URL
    if not YouTubeService.is_valid_youtube_url(project_data.youtube_url):
        raise HTTPException(status_code=400, detail="Invalid YouTube URL")

    # Vytvoř projekt v databázi
    project_service = ProjectService(db)
    project = project_service.create_project(project_data.youtube_url)

    # Pouze načti metadata videa - žádné automatické zpracování
    try:
        youtube_service = YouTubeService()
        video_info = youtube_service.get_video_info(project.youtube_url)

        # Aktualizuj databázový model (ne Pydantic model)
        db_project = db.query(ProjectModel).filter(ProjectModel.project_id == project.project_id).first()
        if db_project:
            db_project.video_title = video_info.get("title", "Neznámý název")
            db_project.video_duration = video_info.get("duration", 0)
            db_project.status = "loaded"  # Nový status - video načteno, čeká na akce
            db_project.updated_at = datetime.utcnow()
            db.commit()
            db.refresh(db_project)

            # Vrať aktualizovaný projekt
            return Project.model_validate(db_project)

    except Exception as e:
        # Aktualizuj databázový model při chybě
        db_project = db.query(ProjectModel).filter(ProjectModel.project_id == project.project_id).first()
        if db_project:
            db_project.status = "error"
            db_project.error_message = f"Chyba při načítání videa: {str(e)}"
            db_project.updated_at = datetime.utcnow()
            db.commit()
        raise HTTPException(status_code=500, detail=f"Chyba při načítání videa: {str(e)}")

    return project


@router.get("/", response_model=List[ProjectSummary])
async def get_projects(db: Session = Depends(get_db)):
    """
    Vrátí seznam všech projektů bez detailů segmentů
    """
    project_service = ProjectService(db)
    return project_service.get_all_projects()

@router.get("/{project_id}", response_model=Project)
async def get_project(project_id: str, db: Session = Depends(get_db)):
    """
    Vrátí detailní informace o projektu včetně segmentů
    """
    project_service = ProjectService(db)
    project = project_service.get_project_by_id(project_id)
    if not project:
        raise HTTPException(status_code=404, detail="Project not found")
    return project


@router.put("/{project_id}/segments/{segment_id}", response_model=SubtitleSegment)
async def update_segment(
    project_id: str,
    segment_id: str,
    update_data: UpdateSegmentRequest,
    db: Session = Depends(get_db)
):
    """
    Aktualizuje text segmentu (uživatelská editace)
    """
    project_service = ProjectService(db)
    segment = project_service.update_segment_text(segment_id, update_data.corrected_text)
    if not segment:
        raise HTTPException(status_code=404, detail="Segment not found")
    return segment

@router.post("/{project_id}/segments/{segment_id}/suggestions/{suggestion_id}/apply")
async def apply_suggestion(
    project_id: str,
    segment_id: str,
    suggestion_id: str,
    db: Session = Depends(get_db)
):
    """
    Aplikuje konkrétní návrh korekce
    """
    project_service = ProjectService(db)
    success = project_service.apply_correction_suggestion(suggestion_id)
    if not success:
        raise HTTPException(status_code=404, detail="Suggestion not found")
    return {"success": True}

@router.post("/{project_id}/segments/{segment_id}/suggestions/{suggestion_id}/reject")
async def reject_suggestion(
    project_id: str,
    segment_id: str,
    suggestion_id: str,
    db: Session = Depends(get_db)
):
    """
    Zamítne konkrétní návrh korekce
    """
    project_service = ProjectService(db)
    success = project_service.reject_correction_suggestion(suggestion_id)
    if not success:
        raise HTTPException(status_code=404, detail="Suggestion not found")
    return {"success": True}

@router.delete("/{project_id}")
async def delete_project(project_id: str, db: Session = Depends(get_db)):
    """
    Smaže projekt a všechna související data
    """
    project_service = ProjectService(db)
    success = project_service.delete_project(project_id)
    if not success:
        raise HTTPException(status_code=404, detail="Project not found")
    return {"message": "Project deleted successfully"}


@router.get("/{project_id}/export/preview")
async def preview_export(project_id: str, db: Session = Depends(get_db)):
    """
    Vrátí preview SRT exportu s validací a statistikami
    """
    project_service = ProjectService(db)
    export_data = project_service.generate_srt_file(project_id, include_preview=True)
    if not export_data:
        raise HTTPException(status_code=404, detail="Project not found or no segments")

    return export_data

@router.post("/{project_id}/export")
async def export_project(project_id: str, db: Session = Depends(get_db)):
    """
    Exportuje projekt do SRT formátu
    """
    from fastapi.responses import Response

    project_service = ProjectService(db)
    export_data = project_service.generate_srt_file(project_id)
    if not export_data:
        raise HTTPException(status_code=404, detail="Project not found or no segments")

    return Response(
        content=export_data["content"],
        media_type="text/plain",
        headers={"Content-Disposition": f"attachment; filename={export_data['filename']}"}
    )

# Nové endpointy pro manuální akce

@router.get("/{project_id}/available-subtitles")
async def get_available_subtitles(project_id: str, db: Session = Depends(get_db)):
    """
    Vrátí seznam dostupných titulků pro video
    """
    project = db.query(ProjectModel).filter(ProjectModel.project_id == project_id).first()
    if not project:
        raise HTTPException(status_code=404, detail="Project not found")

    try:
        youtube_service = YouTubeService()
        available_subtitles = youtube_service.get_available_subtitle_languages(project.youtube_url)
        return {"available_languages": available_subtitles}
    except Exception as e:
        error_msg = str(e)
        if "private" in error_msg.lower() or "unavailable" in error_msg.lower() or "není dostupné" in error_msg:
            # Aktualizuj projekt status
            project.status = "error"
            project.error_message = "Video není dostupné (soukromé nebo smazané)"
            db.commit()
            raise HTTPException(status_code=403, detail="Video není dostupné (soukromé nebo smazané)")
        raise HTTPException(status_code=500, detail=f"Chyba při načítání titulků: {error_msg}")

@router.post("/{project_id}/extract-subtitles")
async def extract_subtitles(
    project_id: str,
    language: str = "cs",
    db: Session = Depends(get_db)
):
    """
    Extrahuje titulky z YouTube videa v zadaném jazyce
    """
    project = db.query(ProjectModel).filter(ProjectModel.project_id == project_id).first()
    if not project:
        raise HTTPException(status_code=404, detail="Project not found")

    try:
        youtube_service = YouTubeService()
        subtitles = youtube_service.extract_subtitles(project.youtube_url, language)

        if not subtitles:
            return {"message": f"Žádné titulky v jazyce '{language}' nebyly nalezeny", "segments_count": 0}

        # Ulož extrahované titulky jako segmenty
        from app.models import SubtitleSegment as SegmentModel
        import uuid

        # Smaž existující segmenty
        db.query(SegmentModel).filter(SegmentModel.project_id == project_id).delete()

        # Přidej nové segmenty z titulků
        for i, subtitle in enumerate(subtitles):
            segment_model = SegmentModel(
                segment_id=str(uuid.uuid4()),
                project_id=project_id,
                sequence_number=subtitle.get('sequence', i + 1),
                start_time_ms=subtitle.get('start_time_ms', 0),
                end_time_ms=subtitle.get('end_time_ms', 0),
                original_text=subtitle.get('text', ''),
                corrected_text=subtitle.get('text', ''),
                status="extracted"
            )
            db.add(segment_model)

        project.status = "subtitles_extracted"
        db.commit()

        return {"message": f"Titulky v jazyce '{language}' úspěšně extrahovány", "segments_count": len(subtitles)}

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Chyba při extrakci titulků: {str(e)}")

@router.post("/{project_id}/download-audio")
async def download_audio(project_id: str, db: Session = Depends(get_db)):
    """
    Stáhne audio z YouTube videa
    """
    project = db.query(ProjectModel).filter(ProjectModel.project_id == project_id).first()
    if not project:
        raise HTTPException(status_code=404, detail="Project not found")

    try:
        youtube_service = YouTubeService()
        from app.core.config import settings
        from pathlib import Path

        temp_dir = Path(settings.TEMP_DIR)
        audio_file_path = youtube_service.download_audio(
            project.youtube_url,
            str(temp_dir),
            project_id
        )

        project.status = "audio_downloaded"
        project.error_message = None
        db.commit()

        return {"message": "Audio úspěšně staženo", "audio_file": audio_file_path}

    except Exception as e:
        project.status = "error"
        project.error_message = f"Chyba při stahování audio: {str(e)}"
        db.commit()
        raise HTTPException(status_code=500, detail=f"Chyba při stahování audio: {str(e)}")

@router.post("/{project_id}/transcribe-whisper")
async def transcribe_with_whisper(project_id: str, db: Session = Depends(get_db)):
    """
    Provede Whisper transkripci audio souboru
    """
    project = db.query(ProjectModel).filter(ProjectModel.project_id == project_id).first()
    if not project:
        raise HTTPException(status_code=404, detail="Project not found")

    try:
        from app.services.openai_service import OpenAIService
        from app.models import SubtitleSegment as SegmentModel
        from app.core.config import settings
        from pathlib import Path
        import uuid

        # Zkontroluj, zda existuje audio soubor
        temp_dir = Path(settings.TEMP_DIR)
        audio_file_path = temp_dir / f"{project_id}_audio.mp3"

        if not audio_file_path.exists():
            raise HTTPException(
                status_code=400,
                detail="Audio soubor nebyl nalezen. Nejdříve stáhněte audio."
            )

        # Inicializuj OpenAI service
        openai_service = OpenAIService()

        # Proveď skutečnou Whisper transkripci
        whisper_segments = await openai_service.transcribe_audio(
            str(audio_file_path),
            language="cs"
        )

        if not whisper_segments:
            raise HTTPException(
                status_code=500,
                detail="Whisper transkripce nevrátila žádné segmenty"
            )

        # Smaž existující segmenty
        db.query(SegmentModel).filter(SegmentModel.project_id == project_id).delete()

        # Přidej Whisper segmenty do databáze
        for i, segment in enumerate(whisper_segments):
            segment_model = SegmentModel(
                segment_id=str(uuid.uuid4()),
                project_id=project_id,
                sequence_number=i + 1,
                start_time_ms=int(segment.get('start', 0) * 1000),
                end_time_ms=int(segment.get('end', 0) * 1000),
                original_text=segment.get('text', ''),
                corrected_text=segment.get('text', ''),
                status="whisper_transcribed"
            )
            db.add(segment_model)

        project.status = "whisper_transcribed"
        project.error_message = None
        project.updated_at = datetime.utcnow()
        db.commit()

        return {"message": "Whisper transkripce dokončena", "segments_count": len(whisper_segments)}

    except Exception as e:
        project.status = "error"
        project.error_message = f"Chyba při Whisper transkripci: {str(e)}"
        project.updated_at = datetime.utcnow()
        db.commit()
        raise HTTPException(status_code=500, detail=f"Chyba při Whisper transkripci: {str(e)}")

@router.post("/{project_id}/correct-ai")
async def correct_with_ai(project_id: str, db: Session = Depends(get_db)):
    """
    Provede AI korekci existujících segmentů
    """
    project = db.query(ProjectModel).filter(ProjectModel.project_id == project_id).first()
    if not project:
        raise HTTPException(status_code=404, detail="Project not found")

    try:
        from app.services.openai_service import OpenAIService
        from app.services.dictionary_service import DictionaryService
        from app.models import SubtitleSegment as SegmentModel, CorrectionSuggestion as SuggestionModel
        import uuid

        # Načti existující segmenty
        segments = db.query(SegmentModel).filter(
            SegmentModel.project_id == project_id
        ).order_by(SegmentModel.sequence_number).all()

        if not segments:
            raise HTTPException(status_code=400, detail="Žádné segmenty k opravě. Nejdříve extrahujte titulky nebo proveďte Whisper transkripci.")

        # Inicializuj služby
        openai_service = OpenAIService()
        dictionary_service = DictionaryService(db)

        # Načti uživatelský slovník
        dictionary_terms = dictionary_service.get_all_dictionary_terms()
        custom_dictionary = [term.phrase for term in dictionary_terms]

        corrections_made = 0
        suggestions_created = 0

        # Zpracuj každý segment
        for segment in segments:
            original_text = segment.original_text

            # Proveď AI korekci pomocí OpenAI GPT
            correction_result = await openai_service.correct_subtitle_text(
                text=original_text,
                language="cs",
                custom_dictionary=custom_dictionary
            )

            corrected_text = correction_result.get("corrected_text", original_text)
            suggestions = correction_result.get("suggestions", [])

            # Aktualizuj segment
            segment.corrected_text = corrected_text
            segment.status = "ai_corrected" if corrected_text != original_text else "unchanged"

            if corrected_text != original_text:
                corrections_made += 1

            # Vytvoř návrhy korekce
            for suggestion in suggestions:
                suggestion_model = SuggestionModel(
                    suggestion_id=str(uuid.uuid4()),
                    segment_id=segment.segment_id,
                    type=suggestion.get("type", "semantic_rewrite"),
                    confidence=suggestion.get("confidence", 0.8),
                    description=suggestion.get("description", "AI korekce"),
                    original_fragment=suggestion.get("original_fragment", original_text),
                    suggested_fragment=suggestion.get("suggested_fragment", corrected_text),
                    applied=True  # Automaticky aplikováno
                )
                db.add(suggestion_model)
                suggestions_created += 1

        project.status = "ai_corrected"
        project.error_message = None
        project.updated_at = datetime.utcnow()
        db.commit()

        return {
            "message": f"AI korekce dokončena. Provedeno {corrections_made} oprav, vytvořeno {suggestions_created} návrhů.",
            "segments_count": len(segments),
            "corrections_made": corrections_made,
            "suggestions_created": suggestions_created
        }

    except Exception as e:
        project.status = "error"
        project.error_message = f"Chyba při AI korekci: {str(e)}"
        project.updated_at = datetime.utcnow()
        db.commit()
        raise HTTPException(status_code=500, detail=f"Chyba při AI korekci: {str(e)}")


@router.post("/{project_id}/export-advanced")
async def export_project_advanced(
    project_id: str,
    export_options: dict = Body(...),
    db: Session = Depends(get_db)
):
    """
    Pokročilý export projektu s možnostmi formátu, kódování a výběru segmentů
    """
    try:
        export_service = AdvancedExportService()

        # Extrahuj možnosti z request body
        format_type = export_options.get('format', 'srt')
        encoding = export_options.get('encoding', 'utf-8')
        include_original = export_options.get('include_original', False)
        include_timestamps = export_options.get('include_timestamps', True)
        selected_segments = export_options.get('selected_segments')
        custom_options = export_options.get('custom_options', {})

        # Proveď export
        result = await export_service.export_subtitles(
            project_id=project_id,
            db=db,
            format_type=format_type,
            encoding=encoding,
            include_original=include_original,
            include_timestamps=include_timestamps,
            selected_segments=selected_segments,
            custom_options=custom_options
        )

        # Vrať výsledek podle požadovaného formátu
        if export_options.get('return_content', False):
            # Vrať obsah přímo v JSON
            return result
        else:
            # Vrať jako soubor ke stažení
            from fastapi.responses import Response
            return Response(
                content=result["content"].encode(encoding),
                media_type="application/octet-stream",
                headers={
                    "Content-Disposition": f"attachment; filename={result['filename']}",
                    "Content-Type": f"text/plain; charset={encoding}"
                }
            )

    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Chyba při exportu: {str(e)}")


@router.get("/{project_id}/export-options")
async def get_export_options(project_id: str, db: Session = Depends(get_db)):
    """
    Získá dostupné možnosti exportu pro projekt
    """
    try:
        # Zkontroluj, zda projekt existuje
        project = db.query(ProjectModel).filter(ProjectModel.project_id == project_id).first()
        if not project:
            raise HTTPException(status_code=404, detail="Project not found")

        # Spočítej segmenty
        from app.models.subtitle_segment import SubtitleSegment as SegmentModel
        segments_count = db.query(SegmentModel).filter(
            SegmentModel.project_id == project_id
        ).count()

        return {
            "supported_formats": list(AdvancedExportService.SUPPORTED_FORMATS),
            "supported_encodings": list(AdvancedExportService.SUPPORTED_ENCODINGS),
            "project_info": {
                "project_id": project_id,
                "title": project.video_title,
                "segments_count": segments_count,
                "status": project.status
            },
            "format_descriptions": {
                "srt": "SubRip Subtitle format - nejběžnější formát pro titulky",
                "vtt": "WebVTT format - moderní webový formát titulků",
                "txt": "Prostý text - pouze text bez časování",
                "json": "JSON format - strukturovaná data s metadaty",
                "csv": "CSV format - tabulková data pro analýzu"
            },
            "encoding_descriptions": {
                "utf-8": "Unicode UTF-8 (doporučeno)",
                "utf-16": "Unicode UTF-16",
                "ascii": "ASCII (pouze anglické znaky)",
                "iso-8859-1": "Latin-1 (západoevropské znaky)"
            }
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Chyba při získávání možností exportu: {str(e)}")