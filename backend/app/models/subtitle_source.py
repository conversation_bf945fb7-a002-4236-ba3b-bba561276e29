from sqlalchemy import Column, String, Integer, Text, ForeignKey, DateTime, Boolean
from sqlalchemy.orm import relationship
from app.core.database import Base
from datetime import datetime


class SubtitleSource(Base):
    """
    Model pro správu různých zdrojů titulků pro jeden projekt.
    Umožňuje uložit a porovnávat YouTube titulky vs. Whisper transkripci.
    """
    __tablename__ = "subtitle_sources"

    source_id = Column(String, primary_key=True)
    project_id = Column(String, ForeignKey("projects.project_id"), nullable=False)
    
    # Typ zdroje
    source_type = Column(String, nullable=False)  # 'youtube_subtitles', 'whisper_transcription'
    source_name = Column(String, nullable=False)  # Lidsky čitelný název
    
    # Metadata
    language = Column(String)  # Jazyk (cs, en, auto)
    is_automatic = Column(Boolean, default=False)  # Automatické vs. manuální titulky
    total_segments = Column(Integer, default=0)
    total_duration_ms = Column(Integer, default=0)
    
    # Status zpracování
    status = Column(String, default="processing")  # 'processing', 'completed', 'error'
    error_message = Column(Text)
    
    # Časové značky
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Cesta k původnímu souboru (pro backup)
    file_path = Column(String)
    
    # Vztahy
    project = relationship("Project", back_populates="subtitle_sources")
    segments = relationship("SubtitleSegment", back_populates="source", cascade="all, delete-orphan")
