from sqlalchemy import Column, String, Integer, Text, ForeignKey, DateTime
from sqlalchemy.orm import relationship
from app.core.database import Base
from datetime import datetime


class SubtitleSegment(Base):
    __tablename__ = "subtitle_segments"

    segment_id = Column(String, primary_key=True)
    project_id = Column(String, ForeignKey("projects.project_id"), nullable=False)
    source_id = Column(String, ForeignKey("subtitle_sources.source_id"), nullable=False)
    sequence_number = Column(Integer, nullable=False)
    start_time_ms = Column(Integer, nullable=False)
    end_time_ms = Column(Integer, nullable=False)

    # Text obsah
    original_text = Column(Text, nullable=False)  # Původní text ze zdroje
    corrected_text = Column(Text, nullable=False)  # AI opravený text

    # Metadata
    confidence_score = Column(Integer)  # Skóre spolehlivosti (0-100)

    status = Column(String, default="NEEDS_REVIEW")
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Vztahy
    project = relationship("Project", back_populates="segments")
    source = relationship("SubtitleSource", back_populates="segments")
    suggestions = relationship("CorrectionSuggestion", back_populates="segment", cascade="all, delete-orphan")