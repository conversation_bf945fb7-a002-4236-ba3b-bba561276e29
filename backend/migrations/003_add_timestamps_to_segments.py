#!/usr/bin/env python3
"""
Migrace 003: <PERSON><PERSON>id<PERSON><PERSON> timestamp sloupců do subtitle_segments
"""

import sqlite3
import sys
from datetime import datetime

def migrate():
    """<PERSON><PERSON><PERSON><PERSON> created_at a updated_at sloupce do subtitle_segments tabulky"""
    
    conn = sqlite3.connect('app.db')
    cursor = conn.cursor()
    
    try:
        print("🔄 Spouštím migraci 003: Přidání timestamp sloupců...")
        
        # Zkontroluj, jestli sloupce už existují
        cursor.execute('PRAGMA table_info(subtitle_segments)')
        columns = [col[1] for col in cursor.fetchall()]
        
        if 'created_at' in columns and 'updated_at' in columns:
            print("✅ Sloupce created_at a updated_at už existují")
            return
        
        # Přidej created_at sloupec
        if 'created_at' not in columns:
            print("➕ Přidávám sloupec created_at...")
            cursor.execute('''
                ALTER TABLE subtitle_segments
                ADD COLUMN created_at TIMESTAMP
            ''')

        # <PERSON><PERSON><PERSON><PERSON> updated_at sloupec
        if 'updated_at' not in columns:
            print("➕ Přidávám sloupec updated_at...")
            cursor.execute('''
                ALTER TABLE subtitle_segments
                ADD COLUMN updated_at TIMESTAMP
            ''')
        
        # Nastav aktuální čas pro existující záznamy
        current_time = datetime.now().isoformat()
        
        if 'created_at' not in columns:
            print("🕒 Nastavujem created_at pro existující záznamy...")
            cursor.execute('''
                UPDATE subtitle_segments 
                SET created_at = ? 
                WHERE created_at IS NULL
            ''', (current_time,))
        
        if 'updated_at' not in columns:
            print("🕒 Nastavujem updated_at pro existující záznamy...")
            cursor.execute('''
                UPDATE subtitle_segments 
                SET updated_at = ? 
                WHERE updated_at IS NULL
            ''', (current_time,))
        
        conn.commit()
        print("✅ Migrace 003 dokončena úspěšně!")
        
        # Ověř výsledek
        cursor.execute('PRAGMA table_info(subtitle_segments)')
        columns = cursor.fetchall()
        print("\n📋 Aktuální struktura subtitle_segments:")
        for col in columns:
            print(f"  {col[1]} ({col[2]})")
        
    except Exception as e:
        conn.rollback()
        print(f"❌ Chyba při migraci: {e}")
        raise
    finally:
        conn.close()

if __name__ == "__main__":
    migrate()
